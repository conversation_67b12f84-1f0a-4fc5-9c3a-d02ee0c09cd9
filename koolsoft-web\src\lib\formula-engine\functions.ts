/**
 * Built-in Functions for KoolSoft Report Formula Engine
 * 
 * This module provides a comprehensive library of mathematical, statistical,
 * and business functions for use in report formulas.
 */

export interface FunctionDefinition {
  name: string;
  description: string;
  parameters: string[];
  examples: string[];
  implementation: (...args: any[]) => any;
}

export class FormulaFunctions {
  private functions: Map<string, FunctionDefinition> = new Map();

  constructor() {
    this.registerBuiltInFunctions();
  }

  /**
   * Register all built-in functions
   */
  private registerBuiltInFunctions(): void {
    // Mathematical functions
    this.register({
      name: 'ABS',
      description: 'Returns the absolute value of a number',
      parameters: ['number'],
      examples: ['ABS(-5) = 5', 'ABS(3.14) = 3.14'],
      implementation: (x: number) => Math.abs(x),
    });

    this.register({
      name: 'ROUND',
      description: 'Rounds a number to specified decimal places',
      parameters: ['number', 'decimals?'],
      examples: ['ROUND(3.14159, 2) = 3.14', 'ROUND(5.7) = 6'],
      implementation: (x: number, decimals: number = 0) => {
        const factor = Math.pow(10, decimals);
        return Math.round(x * factor) / factor;
      },
    });

    this.register({
      name: 'CEIL',
      description: 'Rounds a number up to the nearest integer',
      parameters: ['number'],
      examples: ['CEIL(3.14) = 4', 'CEIL(-2.7) = -2'],
      implementation: (x: number) => Math.ceil(x),
    });

    this.register({
      name: 'FLOOR',
      description: 'Rounds a number down to the nearest integer',
      parameters: ['number'],
      examples: ['FLOOR(3.14) = 3', 'FLOOR(-2.7) = -3'],
      implementation: (x: number) => Math.floor(x),
    });

    this.register({
      name: 'MAX',
      description: 'Returns the maximum value from a list of numbers',
      parameters: ['...numbers'],
      examples: ['MAX(1, 5, 3) = 5', 'MAX(10, 20) = 20'],
      implementation: (...args: number[]) => Math.max(...args),
    });

    this.register({
      name: 'MIN',
      description: 'Returns the minimum value from a list of numbers',
      parameters: ['...numbers'],
      examples: ['MIN(1, 5, 3) = 1', 'MIN(10, 20) = 10'],
      implementation: (...args: number[]) => Math.min(...args),
    });

    this.register({
      name: 'SQRT',
      description: 'Returns the square root of a number',
      parameters: ['number'],
      examples: ['SQRT(16) = 4', 'SQRT(2) = 1.414...'],
      implementation: (x: number) => {
        if (x < 0) throw new Error('Cannot calculate square root of negative number');
        return Math.sqrt(x);
      },
    });

    this.register({
      name: 'POW',
      description: 'Returns base raised to the power of exponent',
      parameters: ['base', 'exponent'],
      examples: ['POW(2, 3) = 8', 'POW(5, 2) = 25'],
      implementation: (base: number, exponent: number) => Math.pow(base, exponent),
    });

    // Statistical functions
    this.register({
      name: 'SUM',
      description: 'Returns the sum of all numbers',
      parameters: ['...numbers'],
      examples: ['SUM(1, 2, 3) = 6', 'SUM(10, 20, 30) = 60'],
      implementation: (...args: number[]) => args.reduce((sum, val) => sum + val, 0),
    });

    this.register({
      name: 'AVERAGE',
      description: 'Returns the average of all numbers',
      parameters: ['...numbers'],
      examples: ['AVERAGE(1, 2, 3) = 2', 'AVERAGE(10, 20, 30) = 20'],
      implementation: (...args: number[]) => {
        if (args.length === 0) return 0;
        return args.reduce((sum, val) => sum + val, 0) / args.length;
      },
    });

    this.register({
      name: 'COUNT',
      description: 'Returns the count of non-null values',
      parameters: ['...values'],
      examples: ['COUNT(1, 2, null, 3) = 3', 'COUNT("a", "b") = 2'],
      implementation: (...args: any[]) => args.filter(val => val !== null && val !== undefined).length,
    });

    this.register({
      name: 'MEDIAN',
      description: 'Returns the median value of numbers',
      parameters: ['...numbers'],
      examples: ['MEDIAN(1, 2, 3) = 2', 'MEDIAN(1, 2, 3, 4) = 2.5'],
      implementation: (...args: number[]) => {
        const sorted = [...args].sort((a, b) => a - b);
        const mid = Math.floor(sorted.length / 2);
        return sorted.length % 2 === 0 
          ? (sorted[mid - 1] + sorted[mid]) / 2 
          : sorted[mid];
      },
    });

    // Business functions
    this.register({
      name: 'PERCENTAGE',
      description: 'Calculates percentage of value relative to total',
      parameters: ['value', 'total'],
      examples: ['PERCENTAGE(25, 100) = 25', 'PERCENTAGE(3, 12) = 25'],
      implementation: (value: number, total: number) => {
        if (total === 0) return 0;
        return (value / total) * 100;
      },
    });

    this.register({
      name: 'GROWTH',
      description: 'Calculates growth percentage between current and previous values',
      parameters: ['current', 'previous'],
      examples: ['GROWTH(120, 100) = 20', 'GROWTH(80, 100) = -20'],
      implementation: (current: number, previous: number) => {
        if (previous === 0) return current > 0 ? 100 : 0;
        return ((current - previous) / previous) * 100;
      },
    });

    this.register({
      name: 'DISCOUNT',
      description: 'Calculates discounted amount',
      parameters: ['amount', 'discountPercent'],
      examples: ['DISCOUNT(100, 10) = 90', 'DISCOUNT(200, 25) = 150'],
      implementation: (amount: number, discountPercent: number) => {
        return amount * (1 - discountPercent / 100);
      },
    });

    this.register({
      name: 'TAX',
      description: 'Calculates tax amount',
      parameters: ['amount', 'taxPercent'],
      examples: ['TAX(100, 18) = 18', 'TAX(500, 12) = 60'],
      implementation: (amount: number, taxPercent: number) => {
        return amount * (taxPercent / 100);
      },
    });

    this.register({
      name: 'MARGIN',
      description: 'Calculates profit margin percentage',
      parameters: ['revenue', 'cost'],
      examples: ['MARGIN(120, 100) = 16.67', 'MARGIN(200, 150) = 25'],
      implementation: (revenue: number, cost: number) => {
        if (revenue === 0) return 0;
        return ((revenue - cost) / revenue) * 100;
      },
    });

    // Date functions
    this.register({
      name: 'DAYS',
      description: 'Calculates days between two dates',
      parameters: ['endDate', 'startDate'],
      examples: ['DAYS("2024-01-31", "2024-01-01") = 30'],
      implementation: (endDate: string | Date, startDate: string | Date) => {
        const end = new Date(endDate);
        const start = new Date(startDate);
        const diffTime = Math.abs(end.getTime() - start.getTime());
        return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      },
    });

    this.register({
      name: 'MONTHS',
      description: 'Calculates months between two dates',
      parameters: ['endDate', 'startDate'],
      examples: ['MONTHS("2024-06-01", "2024-01-01") = 5'],
      implementation: (endDate: string | Date, startDate: string | Date) => {
        const end = new Date(endDate);
        const start = new Date(startDate);
        return (end.getFullYear() - start.getFullYear()) * 12 + 
               (end.getMonth() - start.getMonth());
      },
    });

    this.register({
      name: 'YEARS',
      description: 'Calculates years between two dates',
      parameters: ['endDate', 'startDate'],
      examples: ['YEARS("2026-01-01", "2024-01-01") = 2'],
      implementation: (endDate: string | Date, startDate: string | Date) => {
        const end = new Date(endDate);
        const start = new Date(startDate);
        return end.getFullYear() - start.getFullYear();
      },
    });

    // Conditional functions
    this.register({
      name: 'IF',
      description: 'Returns one value if condition is true, another if false',
      parameters: ['condition', 'trueValue', 'falseValue'],
      examples: ['IF(5 > 3, "Yes", "No") = "Yes"', 'IF(amount > 1000, "High", "Low")'],
      implementation: (condition: any, trueValue: any, falseValue: any) => {
        return condition ? trueValue : falseValue;
      },
    });

    // String functions
    this.register({
      name: 'CONCAT',
      description: 'Concatenates multiple strings',
      parameters: ['...strings'],
      examples: ['CONCAT("Hello", " ", "World") = "Hello World"'],
      implementation: (...args: any[]) => args.map(String).join(''),
    });

    this.register({
      name: 'UPPER',
      description: 'Converts text to uppercase',
      parameters: ['text'],
      examples: ['UPPER("hello") = "HELLO"'],
      implementation: (text: string) => String(text).toUpperCase(),
    });

    this.register({
      name: 'LOWER',
      description: 'Converts text to lowercase',
      parameters: ['text'],
      examples: ['LOWER("HELLO") = "hello"'],
      implementation: (text: string) => String(text).toLowerCase(),
    });

    this.register({
      name: 'LEN',
      description: 'Returns the length of a string',
      parameters: ['text'],
      examples: ['LEN("Hello") = 5'],
      implementation: (text: string) => String(text).length,
    });
  }

  /**
   * Register a new function
   */
  register(definition: FunctionDefinition): void {
    this.functions.set(definition.name.toUpperCase(), definition);
  }

  /**
   * Check if function exists
   */
  hasFunction(name: string): boolean {
    return this.functions.has(name.toUpperCase());
  }

  /**
   * Call a function with arguments
   */
  callFunction(name: string, args: any[]): any {
    const func = this.functions.get(name.toUpperCase());
    if (!func) {
      throw new Error(`Unknown function: ${name}`);
    }

    try {
      return func.implementation(...args);
    } catch (error) {
      throw new Error(`Error in function ${name}: ${error.message}`);
    }
  }

  /**
   * Get function names
   */
  getFunctionNames(): string[] {
    return Array.from(this.functions.keys());
  }

  /**
   * Get function documentation
   */
  getFunctionDoc(name: string): string | undefined {
    const func = this.functions.get(name.toUpperCase());
    if (!func) return undefined;

    return `${func.name}(${func.parameters.join(', ')})
${func.description}

Examples:
${func.examples.map(ex => `  ${ex}`).join('\n')}`;
  }

  /**
   * Get all function definitions
   */
  getAllFunctions(): FunctionDefinition[] {
    return Array.from(this.functions.values());
  }

  /**
   * Get functions by category
   */
  getFunctionsByCategory(): Record<string, FunctionDefinition[]> {
    const categories: Record<string, FunctionDefinition[]> = {
      Mathematical: [],
      Statistical: [],
      Business: [],
      Date: [],
      Conditional: [],
      String: [],
    };

    for (const func of this.functions.values()) {
      // Categorize based on function name patterns
      if (['ABS', 'ROUND', 'CEIL', 'FLOOR', 'SQRT', 'POW', 'MAX', 'MIN'].includes(func.name)) {
        categories.Mathematical.push(func);
      } else if (['SUM', 'AVERAGE', 'COUNT', 'MEDIAN'].includes(func.name)) {
        categories.Statistical.push(func);
      } else if (['PERCENTAGE', 'GROWTH', 'DISCOUNT', 'TAX', 'MARGIN'].includes(func.name)) {
        categories.Business.push(func);
      } else if (['DAYS', 'MONTHS', 'YEARS'].includes(func.name)) {
        categories.Date.push(func);
      } else if (['IF'].includes(func.name)) {
        categories.Conditional.push(func);
      } else if (['CONCAT', 'UPPER', 'LOWER', 'LEN'].includes(func.name)) {
        categories.String.push(func);
      }
    }

    return categories;
  }
}
