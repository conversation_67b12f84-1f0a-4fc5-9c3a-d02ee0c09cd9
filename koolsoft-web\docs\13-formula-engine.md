# Formula Engine Documentation

## Overview

The KoolSoft Report Formula Engine is a comprehensive JavaScript-based calculation engine that enables dynamic formula evaluation within reports. It provides safe parsing and evaluation of mathematical expressions with built-in functions for business calculations.

## Features

- **Safe Expression Parsing**: Secure parsing of mathematical expressions with validation
- **Built-in Functions**: Comprehensive library of mathematical, statistical, and business functions
- **Data Context Integration**: Support for variable substitution from report data
- **Formula Templates**: Pre-built formulas for common business calculations
- **Validation System**: Comprehensive validation with error detection and warnings
- **Test Framework**: Built-in testing capabilities for formula verification

## Architecture

### Core Components

1. **FormulaParser** (`src/lib/formula-engine/parser.ts`)
   - Tokenizes and parses formula expressions into Abstract Syntax Trees (AST)
   - Supports operators, functions, identifiers, literals, and parentheses
   - Provides detailed error reporting with position information

2. **FormulaEvaluator** (`src/lib/formula-engine/evaluator.ts`)
   - Evaluates parsed AST with given context data
   - <PERSON>les type coercion and safe arithmetic operations
   - Supports nested property access (e.g., `customer.name`)

3. **FormulaFunctions** (`src/lib/formula-engine/functions.ts`)
   - Library of built-in functions organized by category
   - Mathematical, statistical, business, date, and string functions
   - Extensible architecture for adding custom functions

4. **FormulaValidator** (`src/lib/formula-engine/validator.ts`)
   - Validates formula syntax and semantics
   - Checks for potential issues like division by zero
   - Provides complexity analysis and warnings

5. **FormulaEngine** (`src/lib/formula-engine/index.ts`)
   - Main interface combining all components
   - Provides unified API for parsing, evaluation, and validation

### Database Schema

#### report_formulas
- Stores formula definitions with metadata
- Supports categorization and template marking
- Tracks usage statistics and complexity scores

#### report_formula_fields
- Links formulas to specific report fields
- Supports multiple report types (AMC, WARRANTY, SERVICE, SALES, CUSTOMER)
- Manages field ordering and activation

#### formula_test_cases
- Stores test cases for formula validation
- Tracks test results and execution history
- Supports automated testing workflows

## API Endpoints

### Formula Management
- `GET /api/reports/formulas` - List formulas with filtering
- `POST /api/reports/formulas` - Create new formula
- `GET /api/reports/formulas/{id}` - Get formula details
- `PUT /api/reports/formulas/{id}` - Update formula
- `DELETE /api/reports/formulas/{id}` - Delete formula

### Formula Evaluation
- `POST /api/reports/formulas/evaluate` - Evaluate formula with context
- `PUT /api/reports/formulas/evaluate` - Validate formula syntax
- `GET /api/reports/formulas/evaluate` - Get available functions

### Templates and Statistics
- `GET /api/reports/formulas/templates` - Get formula templates
- `POST /api/reports/formulas/templates/seed` - Seed default templates
- `GET /api/reports/formulas/statistics` - Get usage statistics

## Built-in Functions

### Mathematical Functions
- `ABS(number)` - Absolute value
- `ROUND(number, decimals?)` - Round to decimal places
- `CEIL(number)` - Round up to nearest integer
- `FLOOR(number)` - Round down to nearest integer
- `MAX(...numbers)` - Maximum value
- `MIN(...numbers)` - Minimum value
- `SQRT(number)` - Square root
- `POW(base, exponent)` - Exponentiation

### Statistical Functions
- `SUM(...numbers)` - Sum of values
- `AVERAGE(...numbers)` - Average of values
- `COUNT(...values)` - Count of non-null values
- `MEDIAN(...numbers)` - Median value

### Business Functions
- `PERCENTAGE(value, total)` - Calculate percentage
- `GROWTH(current, previous)` - Growth percentage
- `DISCOUNT(amount, discountPercent)` - Apply discount
- `TAX(amount, taxPercent)` - Calculate tax
- `MARGIN(revenue, cost)` - Profit margin

### Date Functions
- `DAYS(endDate, startDate)` - Days between dates
- `MONTHS(endDate, startDate)` - Months between dates
- `YEARS(endDate, startDate)` - Years between dates

### Conditional Functions
- `IF(condition, trueValue, falseValue)` - Conditional logic

### String Functions
- `CONCAT(...strings)` - Concatenate strings
- `UPPER(text)` - Convert to uppercase
- `LOWER(text)` - Convert to lowercase
- `LEN(text)` - String length

## Usage Examples

### Basic Arithmetic
```javascript
const engine = new FormulaEngine();

// Simple calculation
const result = engine.evaluate('2 + 3 * 4');
console.log(result.value); // 14

// Using functions
const result2 = engine.evaluate('ROUND(3.14159, 2)');
console.log(result2.value); // 3.14
```

### Context-based Evaluation
```javascript
const context = {
  amount: 1000,
  taxRate: 18,
  customer: { type: 'Premium' }
};

// Tax calculation
const taxResult = engine.evaluate('TAX(amount, taxRate)', context);
console.log(taxResult.value); // 180

// Conditional logic
const discountResult = engine.evaluate(
  'IF(customer.type == "Premium", amount * 0.95, amount)', 
  context
);
console.log(discountResult.value); // 950
```

### Formula Validation
```javascript
const validation = engine.validate('SUM(1, 2, 3) + MAX(4, 5)');
console.log(validation.isValid); // true
console.log(validation.variables); // []
console.log(validation.functions); // ['SUM', 'MAX']
```

## Frontend Components

### FormulaEditor
Rich editor component for creating and editing formulas:
- Syntax highlighting and validation
- Function library browser
- Real-time testing capabilities
- Template integration

### Formula Management Page
Complete interface for managing formulas:
- Formula listing with filtering
- Template gallery
- Statistics dashboard
- CRUD operations

## Security Considerations

1. **Safe Evaluation**: No `eval()` or dynamic code execution
2. **Input Validation**: Comprehensive validation of all inputs
3. **Sandboxing**: Isolated execution environment
4. **Access Control**: Role-based permissions for formula management
5. **Audit Trail**: Complete logging of formula usage and changes

## Performance Optimization

1. **Caching**: AST caching for frequently used formulas
2. **Complexity Limits**: Configurable limits on formula complexity
3. **Lazy Loading**: On-demand function library loading
4. **Batch Processing**: Support for bulk formula evaluation

## Testing

### Unit Tests
- Comprehensive test suite for all components
- Edge case coverage for mathematical operations
- Error handling validation

### Integration Tests
- API endpoint testing
- Database integration verification
- Frontend component testing

### Performance Tests
- Load testing for complex formulas
- Memory usage monitoring
- Execution time benchmarks

## Deployment

### Database Migration
Run the formula tables creation script:
```sql
-- Execute scripts/create-formula-tables.sql
```

### Environment Setup
No additional environment variables required. The formula engine uses existing authentication and database configurations.

### Monitoring
- Formula execution metrics
- Error rate monitoring
- Usage analytics
- Performance tracking

## Troubleshooting

### Common Issues

1. **Parse Errors**: Check formula syntax and function names
2. **Evaluation Errors**: Verify context data and variable names
3. **Performance Issues**: Review formula complexity and optimize
4. **Permission Errors**: Verify user roles and access rights

### Debug Mode
Enable detailed logging for troubleshooting:
```javascript
const engine = new FormulaEngine();
engine.setDebugMode(true);
```

## Future Enhancements

1. **Custom Function Registration**: Allow users to define custom functions
2. **Formula Versioning**: Track formula changes over time
3. **Advanced Analytics**: Machine learning for formula optimization
4. **Visual Formula Builder**: Drag-and-drop formula creation
5. **Real-time Collaboration**: Multi-user formula editing

## Support

For technical support or feature requests related to the Formula Engine:
- Review the API documentation
- Check the test suite for usage examples
- Consult the validation system for error details
- Contact the development team for advanced requirements
