
/* !!! This is code generated by Prisma. Do not edit directly. !!!
/* eslint-disable */

Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('@prisma/client/runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.7.0
 * Query Engine version: 3cff47a7f5d65c3ea74883f1d736e41d68ce91ed
 */
Prisma.prismaVersion = {
  client: "6.7.0",
  engine: "3cff47a7f5d65c3ea74883f1d736e41d68ce91ed"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  name: 'name',
  Phone: 'Phone',
  Desig: 'Desig',
  DescDesig: 'DescDesig'
};

exports.Prisma.LegacyCustomerScalarFieldEnum = {
  id: 'id',
  name: 'name',
  address: 'address',
  location: 'location',
  phone1: 'phone1',
  phone2: 'phone2',
  phone3: 'phone3',
  mobile: 'mobile',
  fax: 'fax',
  email: 'email',
  birthDate: 'birthDate',
  anniversaryDate: 'anniversaryDate',
  birthYear: 'birthYear',
  anniversaryYear: 'anniversaryYear',
  segmentId: 'segmentId',
  designation: 'designation',
  visitCardPath: 'visitCardPath'
};

exports.Prisma.CustomerScalarFieldEnum = {
  id: 'id',
  name: 'name',
  address: 'address',
  city: 'city',
  state: 'state',
  pinCode: 'pinCode',
  phone: 'phone',
  fax: 'fax',
  email: 'email',
  mobile: 'mobile',
  website: 'website',
  freshness: 'freshness',
  lastContact: 'lastContact',
  isActive: 'isActive',
  originalId: 'originalId',
  phone1: 'phone1',
  phone2: 'phone2',
  phone3: 'phone3',
  location: 'location',
  birthDate: 'birthDate',
  birthYear: 'birthYear',
  anniversaryDate: 'anniversaryDate',
  anniversaryYear: 'anniversaryYear',
  segmentId: 'segmentId',
  designation: 'designation',
  visitCardPath: 'visitCardPath',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ContactScalarFieldEnum = {
  id: 'id',
  customerId: 'customerId',
  name: 'name',
  designation: 'designation',
  phone: 'phone',
  email: 'email',
  isPrimary: 'isPrimary',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.VisitCardScalarFieldEnum = {
  id: 'id',
  customerId: 'customerId',
  filePath: 'filePath',
  uploadDate: 'uploadDate',
  notes: 'notes',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.BrandScalarFieldEnum = {
  id: 'id',
  name: 'name'
};

exports.Prisma.BrandsScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  isActive: 'isActive',
  originalId: 'originalId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ProductScalarFieldEnum = {
  id: 'id',
  name: 'name',
  TaplTax: 'TaplTax',
  BslTax: 'BslTax'
};

exports.Prisma.ProductsScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  isActive: 'isActive',
  originalId: 'originalId',
  taplTax: 'taplTax',
  bslTax: 'bslTax',
  brandId: 'brandId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ModelScalarFieldEnum = {
  id: 'id',
  name: 'name',
  productId: 'productId',
  specs: 'specs',
  Ton_cap: 'Ton_cap',
  InstChg: 'InstChg',
  NoOfComp: 'NoOfComp',
  BslMRP: 'BslMRP',
  BslMCP: 'BslMCP',
  BslCP: 'BslCP',
  TaplMRP: 'TaplMRP',
  TaplMCP: 'TaplMCP',
  TaplCP: 'TaplCP',
  MRPGM: 'MRPGM',
  MCPGM: 'MCPGM'
};

exports.Prisma.ModelsScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  specs: 'specs',
  tonnage: 'tonnage',
  installCharge: 'installCharge',
  numberOfComponents: 'numberOfComponents',
  bslMRP: 'bslMRP',
  bslMCP: 'bslMCP',
  bslCP: 'bslCP',
  taplMRP: 'taplMRP',
  taplMCP: 'taplMCP',
  taplCP: 'taplCP',
  mrpGM: 'mrpGM',
  mcpGM: 'mcpGM',
  isActive: 'isActive',
  originalId: 'originalId',
  productId: 'productId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.AMCContractScalarFieldEnum = {
  originalId: 'originalId',
  customerId: 'customerId',
  executiveId: 'executiveId',
  contactPerson: 'contactPerson',
  contactPhone: 'contactPhone',
  natureOfService: 'natureOfService',
  startDate: 'startDate',
  endDate: 'endDate',
  warningDate: 'warningDate',
  amount: 'amount',
  bslDebit: 'bslDebit',
  previousAmount: 'previousAmount',
  amcPeriod: 'amcPeriod',
  yearOfCommencement: 'yearOfCommencement',
  numberOfMachines: 'numberOfMachines',
  numberOfServices: 'numberOfServices',
  renewalFlag: 'renewalFlag',
  newId: 'newId',
  outId: 'outId',
  blstrFlag: 'blstrFlag',
  paidAmount: 'paidAmount',
  fresh: 'fresh',
  numberOfInstallments: 'numberOfInstallments',
  paymentMode: 'paymentMode',
  totalTonnage: 'totalTonnage',
  category: 'category'
};

exports.Prisma.Amc_contractsScalarFieldEnum = {
  id: 'id',
  customerId: 'customerId',
  executiveId: 'executiveId',
  contactPersonId: 'contactPersonId',
  natureOfService: 'natureOfService',
  startDate: 'startDate',
  endDate: 'endDate',
  warningDate: 'warningDate',
  amount: 'amount',
  bslDebit: 'bslDebit',
  previousAmount: 'previousAmount',
  amcPeriod: 'amcPeriod',
  yearOfCommencement: 'yearOfCommencement',
  numberOfMachines: 'numberOfMachines',
  numberOfServices: 'numberOfServices',
  renewalFlag: 'renewalFlag',
  blstrFlag: 'blstrFlag',
  paidAmount: 'paidAmount',
  fresh: 'fresh',
  numberOfInstallments: 'numberOfInstallments',
  paymentMode: 'paymentMode',
  totalTonnage: 'totalTonnage',
  category: 'category',
  status: 'status',
  originalId: 'originalId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  usersId: 'usersId'
};

exports.Prisma.AMCMachineScalarFieldEnum = {
  amcId: 'amcId',
  PrdID: 'PrdID',
  modelId: 'modelId',
  BrID: 'BrID',
  location: 'location',
  LoctFlag: 'LoctFlag',
  serialNumber: 'serialNumber',
  assetNo: 'assetNo',
  historyCardNo: 'historyCardNo',
  Section: 'Section'
};

exports.Prisma.Amc_machinesScalarFieldEnum = {
  id: 'id',
  amcContractId: 'amcContractId',
  productId: 'productId',
  modelId: 'modelId',
  brandId: 'brandId',
  location: 'location',
  locationFlag: 'locationFlag',
  serialNumber: 'serialNumber',
  assetNo: 'assetNo',
  historyCardNo: 'historyCardNo',
  section: 'section',
  originalAmcId: 'originalAmcId',
  originalAssetNo: 'originalAssetNo',
  originalHistoryCardNo: 'originalHistoryCardNo',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.AMCComponentScalarFieldEnum = {
  amcId: 'amcId',
  assetNo: 'assetNo',
  componentNo: 'componentNo',
  serialNumber: 'serialNumber',
  WDate: 'WDate',
  Section: 'Section'
};

exports.Prisma.Amc_componentsScalarFieldEnum = {
  id: 'id',
  machineId: 'machineId',
  componentNo: 'componentNo',
  serialNumber: 'serialNumber',
  warrantyDate: 'warrantyDate',
  section: 'section',
  originalAmcId: 'originalAmcId',
  originalAssetNo: 'originalAssetNo',
  originalComponentNo: 'originalComponentNo',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.Amc_paymentsScalarFieldEnum = {
  id: 'id',
  amcContractId: 'amcContractId',
  receiptNo: 'receiptNo',
  paymentDate: 'paymentDate',
  paymentMode: 'paymentMode',
  amount: 'amount',
  particulars: 'particulars',
  originalAmcId: 'originalAmcId',
  originalReceiptNo: 'originalReceiptNo',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.AMCServiceDateScalarFieldEnum = {
  amcId: 'amcId',
  serviceDate: 'serviceDate',
  serviceFlag: 'serviceFlag',
  completedDate: 'completedDate',
  serviceNumber: 'serviceNumber',
  serviceId: 'serviceId'
};

exports.Prisma.Amc_service_datesScalarFieldEnum = {
  id: 'id',
  amcContractId: 'amcContractId',
  serviceDate: 'serviceDate',
  serviceFlag: 'serviceFlag',
  completedDate: 'completedDate',
  serviceNumber: 'serviceNumber',
  serviceId: 'serviceId',
  originalAmcId: 'originalAmcId',
  originalServiceDate: 'originalServiceDate',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.DivisionScalarFieldEnum = {
  id: 'id',
  name: 'name'
};

exports.Prisma.DivisionsScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  isActive: 'isActive',
  originalId: 'originalId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.AMCDivisionScalarFieldEnum = {
  amcId: 'amcId',
  divisionId: 'divisionId'
};

exports.Prisma.Amc_divisionsScalarFieldEnum = {
  id: 'id',
  amcContractId: 'amcContractId',
  divisionId: 'divisionId',
  percentage: 'percentage',
  isPrimary: 'isPrimary',
  originalAmcId: 'originalAmcId',
  originalDivisionId: 'originalDivisionId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.InWarrantyScalarFieldEnum = {
  originalId: 'originalId',
  customerId: 'customerId',
  executiveId: 'executiveId',
  contactPerson: 'contactPerson',
  contactPhone: 'contactPhone',
  bslNo: 'bslNo',
  bslDate: 'bslDate',
  bslAmount: 'bslAmount',
  frequency: 'frequency',
  numberOfMachines: 'numberOfMachines',
  installDate: 'installDate',
  warrantyDate: 'warrantyDate',
  warningDate: 'warningDate',
  technicianId: 'technicianId',
  amcId: 'amcId',
  outId: 'outId'
};

exports.Prisma.WarrantiesScalarFieldEnum = {
  id: 'id',
  customerId: 'customerId',
  executiveId: 'executiveId',
  contactPersonId: 'contactPersonId',
  bslNo: 'bslNo',
  bslDate: 'bslDate',
  bslAmount: 'bslAmount',
  frequency: 'frequency',
  numberOfMachines: 'numberOfMachines',
  installDate: 'installDate',
  warrantyDate: 'warrantyDate',
  warningDate: 'warningDate',
  technicianId: 'technicianId',
  amcId: 'amcId',
  status: 'status',
  originalId: 'originalId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  warrantyUpdateDate: 'warrantyUpdateDate',
  usersId: 'usersId'
};

exports.Prisma.InWarrantyMachineScalarFieldEnum = {
  inWarrantyId: 'inWarrantyId',
  productId: 'productId',
  modelId: 'modelId',
  location: 'location',
  locationFlag: 'locationFlag',
  serialNumber: 'serialNumber',
  assetNo: 'assetNo',
  historyCardNo: 'historyCardNo',
  section: 'section'
};

exports.Prisma.Warranty_machinesScalarFieldEnum = {
  id: 'id',
  warrantyId: 'warrantyId',
  productId: 'productId',
  modelId: 'modelId',
  brandId: 'brandId',
  location: 'location',
  locationFlag: 'locationFlag',
  serialNumber: 'serialNumber',
  assetNo: 'assetNo',
  historyCardNo: 'historyCardNo',
  section: 'section',
  originalWarrantyId: 'originalWarrantyId',
  originalAssetNo: 'originalAssetNo',
  originalHistoryCardNo: 'originalHistoryCardNo',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.InWarrantyComponentScalarFieldEnum = {
  inWarrantyId: 'inWarrantyId',
  assetNo: 'assetNo',
  componentNo: 'componentNo',
  serialNumber: 'serialNumber',
  warrantyDate: 'warrantyDate',
  section: 'section'
};

exports.Prisma.Warranty_componentsScalarFieldEnum = {
  id: 'id',
  machineId: 'machineId',
  componentNo: 'componentNo',
  serialNumber: 'serialNumber',
  warrantyDate: 'warrantyDate',
  section: 'section',
  originalWarrantyId: 'originalWarrantyId',
  originalAssetNo: 'originalAssetNo',
  originalComponentNo: 'originalComponentNo',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ACC_DIVISIONScalarFieldEnum = {
  AccDivId: 'AccDivId',
  Name: 'Name'
};

exports.Prisma.AMC_AddOnScalarFieldEnum = {
  AmcID: 'AmcID',
  PrdID: 'PrdID',
  MdlID: 'MdlID',
  BrID: 'BrID',
  Loct: 'Loct',
  LoctFlag: 'LoctFlag',
  MCSlNo: 'MCSlNo',
  AssetNo: 'AssetNo',
  HCNo: 'HCNo',
  Amt: 'Amt',
  Section: 'Section'
};

exports.Prisma.AMC_AddOnCompScalarFieldEnum = {
  AmcID: 'AmcID',
  AssetNo: 'AssetNo',
  CompNo: 'CompNo',
  CompSlNo: 'CompSlNo',
  WDate: 'WDate',
  Section: 'Section'
};

exports.Prisma.AMC_NewLoctScalarFieldEnum = {
  AmcID: 'AmcID',
  AssetNo: 'AssetNo',
  NewLoct: 'NewLoct'
};

exports.Prisma.BluestarAccounts_SumScalarFieldEnum = {
  BlrAccId: 'BlrAccId',
  CustId: 'CustId',
  BslDate: 'BslDate',
  Mode: 'Mode',
  AccDivId: 'AccDivId',
  BslExec: 'BslExec'
};

exports.Prisma.CATEGORY_SRVScalarFieldEnum = {
  CatSrvId: 'CatSrvId',
  Name: 'Name'
};

exports.Prisma.COMPETITORScalarFieldEnum = {
  ID: 'ID',
  Name: 'Name'
};

exports.Prisma.COMPLAINTScalarFieldEnum = {
  ID: 'ID',
  Name: 'Name'
};

exports.Prisma.CRMScalarFieldEnum = {
  RecID: 'RecID',
  RecDate: 'RecDate',
  CustID: 'CustID',
  CustST: 'CustST',
  CPer: 'CPer',
  SegId: 'SegId',
  UserGrpId: 'UserGrpId',
  BrID: 'BrID',
  IsBarred: 'IsBarred',
  SrvRestrict: 'SrvRestrict',
  StLevel: 'StLevel',
  Place: 'Place',
  Relation: 'Relation',
  PrId: 'PrId',
  NatSrvId: 'NatSrvId',
  USP: 'USP',
  ExecID: 'ExecID',
  IsBLost: 'IsBLost',
  BRelation: 'BRelation'
};

exports.Prisma.Cust_FreshScalarFieldEnum = {
  ID: 'ID',
  Name: 'Name',
  CustAdd: 'CustAdd',
  CustPhone1: 'CustPhone1',
  CustPhone2: 'CustPhone2',
  CustFax: 'CustFax',
  CustEmail: 'CustEmail'
};

exports.Prisma.DEBIT_DIVScalarFieldEnum = {
  ID: 'ID',
  DebitDiv: 'DebitDiv'
};

exports.Prisma.DEDUCTION_HEADScalarFieldEnum = {
  DedHeadId: 'DedHeadId',
  Head: 'Head'
};

exports.Prisma.ENQUIRYScalarFieldEnum = {
  ID: 'ID',
  Name: 'Name'
};

exports.Prisma.EnqCompScalarFieldEnum = {
  CallID: 'CallID',
  CDate: 'CDate',
  CTime: 'CTime',
  CustId: 'CustId',
  CustST: 'CustST',
  Status: 'Status',
  Remarks: 'Remarks',
  No: 'No',
  CallType: 'CallType',
  DTime: 'DTime',
  PFlag: 'PFlag'
};

exports.Prisma.ExecTargetScalarFieldEnum = {
  ExecId: 'ExecId',
  Month: 'Month',
  Year: 'Year',
  PrdId: 'PrdId',
  Target: 'Target',
  Billed: 'Billed'
};

exports.Prisma.FAILUREScalarFieldEnum = {
  ID: 'ID',
  Name: 'Name'
};

exports.Prisma.Funnel_divScalarFieldEnum = {
  FID: 'FID',
  DivID: 'DivID'
};

exports.Prisma.Funnel_sumScalarFieldEnum = {
  TID: 'TID',
  TStatus: 'TStatus',
  FID: 'FID',
  FDate: 'FDate',
  LogDate: 'LogDate',
  Prospect: 'Prospect',
  ProsPerct: 'ProsPerct',
  FPDate: 'FPDate',
  NVDate: 'NVDate',
  OrdST: 'OrdST',
  LostReason: 'LostReason',
  CPerson: 'CPerson',
  Phone: 'Phone',
  Remarks: 'Remarks'
};

exports.Prisma.Gateway_divScalarFieldEnum = {
  GID: 'GID',
  DivID: 'DivID'
};

exports.Prisma.Gateway_sumScalarFieldEnum = {
  TID: 'TID',
  TStatus: 'TStatus',
  GID: 'GID',
  GTDate: 'GTDate',
  LogDate: 'LogDate',
  Prospect: 'Prospect',
  ProsPerct: 'ProsPerct',
  FPDate: 'FPDate',
  NVDate: 'NVDate',
  OrdST: 'OrdST',
  LostReason: 'LostReason',
  CPerson: 'CPerson',
  Phone: 'Phone',
  Remarks: 'Remarks'
};

exports.Prisma.HistorySectionScalarFieldEnum = {
  Card_No: 'Card_No',
  Section: 'Section'
};

exports.Prisma.History_AddOnDetScalarFieldEnum = {
  Card_No: 'Card_No',
  WarnNo: 'WarnNo',
  HandDate: 'HandDate',
  ExpDate: 'ExpDate',
  ExpCmpDate: 'ExpCmpDate'
};

exports.Prisma.History_AmcDetScalarFieldEnum = {
  Card_No: 'Card_No',
  AmcID: 'AmcID',
  BlkYear: 'BlkYear',
  Debit: 'Debit',
  NCBonus: 'NCBonus',
  Increments: 'Increments'
};

exports.Prisma.History_SumScalarFieldEnum = {
  Card_No: 'Card_No',
  CustID: 'CustID',
  CPerson: 'CPerson',
  Phone: 'Phone',
  AddonFlag: 'AddonFlag',
  Source: 'Source',
  ToCardNo: 'ToCardNo'
};

exports.Prisma.InwInst_ParamScalarFieldEnum = {
  InwID: 'InwID',
  AssetNo: 'AssetNo',
  SP: 'SP',
  DP: 'DP',
  AMP: 'AMP',
  VOLT: 'VOLT',
  CFM: 'CFM',
  Noise: 'Noise',
  RTemp: 'RTemp',
  ATemp: 'ATemp'
};

exports.Prisma.Inwarranty_NewLoctScalarFieldEnum = {
  InwId: 'InwId',
  AssetNo: 'AssetNo',
  NewLoct: 'NewLoct'
};

exports.Prisma.Inwarranty_SerDatesScalarFieldEnum = {
  InwID: 'InwID',
  SerDates: 'SerDates',
  SerFlag: 'SerFlag',
  DoneDt: 'DoneDt',
  SrNo: 'SrNo',
  ServID: 'ServID'
};

exports.Prisma.Inwarranty_TrDamScalarFieldEnum = {
  InwID: 'InwID',
  AssetNo: 'AssetNo',
  TrID: 'TrID'
};

exports.Prisma.Inwarranty_divScalarFieldEnum = {
  InwID: 'InwID',
  DivID: 'DivID'
};

exports.Prisma.MovementRegister_ProductsScalarFieldEnum = {
  RegId: 'RegId',
  RecId: 'RecId',
  ProdId: 'ProdId',
  Qnty: 'Qnty',
  MacSlno: 'MacSlno',
  CompSlNo: 'CompSlNo',
  PartName: 'PartName',
  PartSlNo: 'PartSlNo',
  ReceiptNo: 'ReceiptNo'
};

exports.Prisma.MovementRegister_SumScalarFieldEnum = {
  RegId: 'RegId',
  CustId: 'CustId',
  DOR: 'DOR',
  TechId: 'TechId',
  NatSrvId: 'NatSrvId',
  PDD: 'PDD'
};

exports.Prisma.NATURE_SERVICEScalarFieldEnum = {
  NatSrvId: 'NatSrvId',
  Name: 'Name'
};

exports.Prisma.NAT_COMPLAINTScalarFieldEnum = {
  ID: 'ID',
  Name: 'Name'
};

exports.Prisma.ORG_DIVScalarFieldEnum = {
  ID: 'ID',
  OrgDiv: 'OrgDiv'
};

exports.Prisma.OrderFormScalarFieldEnum = {
  OrdFrmNo: 'OrdFrmNo',
  TID: 'TID',
  OrdfrmDate: 'OrdfrmDate',
  ToAdd: 'ToAdd',
  Attn: 'Attn',
  OrdPlace: 'OrdPlace',
  CustOrdNo: 'CustOrdNo',
  CustAdd: 'CustAdd',
  Price: 'Price',
  Payment: 'Payment',
  DelPlace: 'DelPlace',
  WorstDel: 'WorstDel',
  ForwById: 'ForwById'
};

exports.Prisma.OrderForm_MdlScalarFieldEnum = {
  OrdFrmNo: 'OrdFrmNo',
  PrdId: 'PrdId',
  MdlId: 'MdlId',
  Qty: 'Qty'
};

exports.Prisma.Order_divScalarFieldEnum = {
  OrdID: 'OrdID',
  DivID: 'DivID'
};

exports.Prisma.Order_payScalarFieldEnum = {
  OrdID: 'OrdID',
  ReceiptNo: 'ReceiptNo',
  PayDate: 'PayDate',
  Mode: 'Mode',
  Amt: 'Amt',
  Particulars: 'Particulars',
  BlstrFlg: 'BlstrFlg'
};

exports.Prisma.Order_sumScalarFieldEnum = {
  TID: 'TID',
  TStatus: 'TStatus',
  OrdID: 'OrdID',
  OrdDate: 'OrdDate',
  LogDate: 'LogDate',
  TaplNo: 'TaplNo',
  TaplDate: 'TaplDate',
  BslInvNo: 'BslInvNo',
  BslInvDate: 'BslInvDate',
  FrmWhere: 'FrmWhere',
  Prospect: 'Prospect',
  ProsPerct: 'ProsPerct',
  FPDate: 'FPDate',
  DelDate: 'DelDate',
  Delay: 'Delay',
  ActDate: 'ActDate',
  TaplAmt: 'TaplAmt',
  BslAmt: 'BslAmt',
  BslAdv: 'BslAdv',
  BslCollect: 'BslCollect',
  TaplAdv: 'TaplAdv',
  TaplCollect: 'TaplCollect',
  DelAdd: 'DelAdd',
  InstaChgsTo: 'InstaChgsTo',
  CPerson: 'CPerson',
  Phone: 'Phone',
  Remarks: 'Remarks'
};

exports.Prisma.OutWnty_CompScalarFieldEnum = {
  OutID: 'OutID',
  AssetNo: 'AssetNo',
  CompNo: 'CompNo',
  CompSlNo: 'CompSlNo',
  WDate: 'WDate',
  Section: 'Section'
};

exports.Prisma.OutWnty_MachineScalarFieldEnum = {
  OutID: 'OutID',
  PrdID: 'PrdID',
  MdlID: 'MdlID',
  BrID: 'BrID',
  Loct: 'Loct',
  MCSlNo: 'MCSlNo',
  AssetNo: 'AssetNo',
  HCNo: 'HCNo',
  Section: 'Section'
};

exports.Prisma.OutWnty_divScalarFieldEnum = {
  OutID: 'OutID',
  DivId: 'DivId'
};

exports.Prisma.OutWnty_sumScalarFieldEnum = {
  OutID: 'OutID',
  OutDate: 'OutDate',
  CustID: 'CustID',
  ExecID: 'ExecID',
  Cperson: 'Cperson',
  Phone: 'Phone',
  NoMC: 'NoMC',
  Source: 'Source',
  AmcID: 'AmcID',
  InwID: 'InwID',
  ToAmcID: 'ToAmcID'
};

exports.Prisma.PRIORITY_LOIScalarFieldEnum = {
  PrId: 'PrId',
  Name: 'Name'
};

exports.Prisma.PROFORMA_MODELScalarFieldEnum = {
  PMdlId: 'PMdlId',
  Name: 'Name',
  Prod_Id: 'Prod_Id',
  Description: 'Description',
  Price: 'Price'
};

exports.Prisma.Perf_Inv_SumScalarFieldEnum = {
  QtNo: 'QtNo',
  QtDate: 'QtDate',
  TID: 'TID',
  TStatus: 'TStatus',
  RefNo: 'RefNo',
  RefText: 'RefText',
  Delay: 'Delay',
  InstChg: 'InstChg',
  Discount: 'Discount',
  Adv: 'Adv',
  DelAmt: 'DelAmt',
  CommAmt: 'CommAmt',
  OrderBsl: 'OrderBsl',
  OrderTapl: 'OrderTapl'
};

exports.Prisma.Perf_MachineScalarFieldEnum = {
  QtNo: 'QtNo',
  PrdID: 'PrdID',
  MdlID: 'MdlID',
  Qty: 'Qty',
  QtPrice: 'QtPrice',
  TaxRate: 'TaxRate'
};

exports.Prisma.Pipeline_divScalarFieldEnum = {
  PPID: 'PPID',
  DivID: 'DivID'
};

exports.Prisma.Pipeline_sumScalarFieldEnum = {
  TID: 'TID',
  TStatus: 'TStatus',
  PPID: 'PPID',
  PDate: 'PDate',
  LogDate: 'LogDate',
  Prospect: 'Prospect',
  ProsPerct: 'ProsPerct',
  FPDate: 'FPDate',
  NVDate: 'NVDate',
  OrdST: 'OrdST',
  LostReason: 'LostReason',
  CPerson: 'CPerson',
  Phone: 'Phone',
  Remarks: 'Remarks'
};

exports.Prisma.Proforma_SumScalarFieldEnum = {
  PInvNo: 'PInvNo',
  PInvDate: 'PInvDate',
  CustId: 'CustId',
  RefNo: 'RefNo',
  DivId: 'DivId',
  SegId: 'SegId',
  ExecId: 'ExecId',
  Delay: 'Delay',
  Adv: 'Adv',
  DelAmt: 'DelAmt',
  CommAmt: 'CommAmt',
  OrderBsl: 'OrderBsl',
  OrderTapl: 'OrderTapl',
  Remarks: 'Remarks',
  Status: 'Status',
  AuthId: 'AuthId',
  ForwId: 'ForwId',
  DelAddr: 'DelAddr',
  Greetings: 'Greetings'
};

exports.Prisma.Rel_StatisticsScalarFieldEnum = {
  RecID: 'RecID',
  RecDate: 'RecDate',
  CustID: 'CustID',
  CustST: 'CustST',
  MetDate: 'MetDate',
  ExecID: 'ExecID',
  CPer: 'CPer',
  Phone: 'Phone',
  FeedBack: 'FeedBack',
  NVDate: 'NVDate'
};

exports.Prisma.Rpt_AmcTmpScalarFieldEnum = {
  AmcId: 'AmcId',
  AssetNo: 'AssetNo',
  PrdId: 'PrdId',
  MdlId: 'MdlId',
  MCSlNo: 'MCSlNo',
  CompSlNo: 'CompSlNo',
  Location: 'Location',
  Wnty: 'Wnty',
  NoMC: 'NoMC',
  HCNo: 'HCNo',
  CompNo: 'CompNo',
  AmcAmt: 'AmcAmt',
  BrId: 'BrId'
};

exports.Prisma.SEGMENTScalarFieldEnum = {
  ID: 'ID',
  Name: 'Name'
};

exports.Prisma.SER_VISITScalarFieldEnum = {
  ID: 'ID',
  SerVisit: 'SerVisit'
};

exports.Prisma.SOMSScalarFieldEnum = {
  CallID: 'CallID',
  CDate: 'CDate',
  CustId: 'CustId',
  CustST: 'CustST',
  CPer: 'CPer',
  CPhone: 'CPhone',
  PrId: 'PrId',
  DownDate: 'DownDate',
  DownTime: 'DownTime',
  NatSrvId: 'NatSrvId',
  UserId: 'UserId',
  TechId: 'TechId',
  FaultRpt: 'FaultRpt',
  OutUserId: 'OutUserId',
  CatSrvId: 'CatSrvId',
  PartFailure: 'PartFailure',
  UpTime: 'UpTime',
  UpDt: 'UpDt',
  Rect: 'Rect',
  RectTechId: 'RectTechId',
  StLevel: 'StLevel',
  TAT: 'TAT',
  SRNo: 'SRNo',
  StMac: 'StMac',
  FTechId: 'FTechId',
  Remarks: 'Remarks',
  InvNo: 'InvNo',
  Amt: 'Amt'
};

exports.Prisma.SPAREScalarFieldEnum = {
  ID: 'ID',
  Name: 'Name',
  WtAvgCost: 'WtAvgCost',
  MRP: 'MRP'
};

exports.Prisma.ServiceRpt_ComplaintScalarFieldEnum = {
  SRNo: 'SRNo',
  CompID: 'CompID',
  NatID: 'NatID'
};

exports.Prisma.ServiceRpt_DebitDivScalarFieldEnum = {
  SRNo: 'SRNo',
  DebitDivId: 'DebitDivId'
};

exports.Prisma.ServiceRpt_FailureScalarFieldEnum = {
  SRNo: 'SRNo',
  FailID: 'FailID',
  Remedy: 'Remedy'
};

exports.Prisma.ServiceRpt_MeasureScalarFieldEnum = {
  SRNo: 'SRNo',
  SP: 'SP',
  DP: 'DP',
  Amp: 'Amp',
  Volt: 'Volt',
  CFM: 'CFM',
  Noise: 'Noise',
  RTemp: 'RTemp',
  ATemp: 'ATemp',
  TimeOn: 'TimeOn',
  GCT: 'GCT'
};

exports.Prisma.ServiceRpt_OrgDivScalarFieldEnum = {
  SRNO: 'SRNO',
  OrgDivID: 'OrgDivID'
};

exports.Prisma.ServiceRpt_SpareScalarFieldEnum = {
  SRNo: 'SRNo',
  SpID: 'SpID',
  Qty: 'Qty',
  Price: 'Price'
};

exports.Prisma.ServiceRpt_VisitScalarFieldEnum = {
  SRNo: 'SRNo',
  SerVisitID: 'SerVisitID'
};

exports.Prisma.ServiceRpt_sumScalarFieldEnum = {
  SRNo: 'SRNo',
  SRDate: 'SRDate',
  HCNo: 'HCNo',
  AssetNo: 'AssetNo',
  FirstRes: 'FirstRes',
  WorkDate: 'WorkDate',
  RmDate: 'RmDate',
  RetDate: 'RetDate',
  LstSRNo: 'LstSRNo',
  Item: 'Item',
  Fault: 'Fault',
  Action: 'Action',
  FollowUp: 'FollowUp',
  ServID: 'ServID'
};

exports.Prisma.SpareCostScalarFieldEnum = {
  PurNo: 'PurNo',
  PDate: 'PDate',
  SpID: 'SpID',
  Qty: 'Qty',
  Amt: 'Amt',
  UnitCost: 'UnitCost'
};

exports.Prisma.TAXScalarFieldEnum = {
  TaxId: 'TaxId',
  TaxRate: 'TaxRate'
};

exports.Prisma.TERRITORYScalarFieldEnum = {
  TerrId: 'TerrId',
  Terr: 'Terr'
};

exports.Prisma.TRANSIT_DAMAGEScalarFieldEnum = {
  TrID: 'TrID',
  TrDamage: 'TrDamage'
};

exports.Prisma.TransactionScalarFieldEnum = {
  TID: 'TID',
  TStatus: 'TStatus',
  CustID: 'CustID',
  ExecID: 'ExecID',
  TerrId: 'TerrId',
  SegID: 'SegID',
  EnqID: 'EnqID',
  VTID: 'VTID',
  STFlag: 'STFlag',
  CustSt: 'CustSt',
  Ideac: 'Ideac',
  QtNo: 'QtNo',
  TimeElapsed: 'TimeElapsed'
};

exports.Prisma.Transaction_BiasScalarFieldEnum = {
  TID: 'TID',
  BiasID: 'BiasID'
};

exports.Prisma.Transaction_MachineScalarFieldEnum = {
  TID: 'TID',
  PrdID: 'PrdID',
  MdlID: 'MdlID',
  Qty: 'Qty',
  QtPrice: 'QtPrice',
  TaxRate: 'TaxRate',
  SalesMargin: 'SalesMargin',
  BlStar: 'BlStar'
};

exports.Prisma.Transaction_USPScalarFieldEnum = {
  TID: 'TID',
  USPID: 'USPID'
};

exports.Prisma.USERGROUPScalarFieldEnum = {
  UserGrpId: 'UserGrpId',
  Name: 'Name'
};

exports.Prisma.USPScalarFieldEnum = {
  ID: 'ID',
  Name: 'Name'
};

exports.Prisma.UserPwdScalarFieldEnum = {
  UserID: 'UserID',
  User: 'User',
  Pwd: 'Pwd',
  Key: 'Key'
};

exports.Prisma.VISITScalarFieldEnum = {
  ID: 'ID',
  Name: 'Name'
};

exports.Prisma.Out_warrantiesScalarFieldEnum = {
  id: 'id',
  customerId: 'customerId',
  executiveId: 'executiveId',
  contactPerson: 'contactPerson',
  contactPhone: 'contactPhone',
  startDate: 'startDate',
  endDate: 'endDate',
  amount: 'amount',
  source: 'source',
  sourceId: 'sourceId',
  amcSourceId: 'amcSourceId',
  inWarrantySourceId: 'inWarrantySourceId',
  isActive: 'isActive',
  originalId: 'originalId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.Out_warranty_machinesScalarFieldEnum = {
  id: 'id',
  outWarrantyId: 'outWarrantyId',
  modelId: 'modelId',
  serialNumber: 'serialNumber',
  location: 'location',
  originalOutWarrantyId: 'originalOutWarrantyId',
  originalAssetNo: 'originalAssetNo',
  originalHistoryCardNo: 'originalHistoryCardNo',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.Out_warranty_componentsScalarFieldEnum = {
  id: 'id',
  machineId: 'machineId',
  type: 'type',
  serialNumber: 'serialNumber',
  originalOutWarrantyId: 'originalOutWarrantyId',
  originalAssetNo: 'originalAssetNo',
  originalComponentNo: 'originalComponentNo',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  out_warrantiesId: 'out_warrantiesId'
};

exports.Prisma.Out_warranty_paymentsScalarFieldEnum = {
  id: 'id',
  outWarrantyId: 'outWarrantyId',
  receiptNo: 'receiptNo',
  paymentDate: 'paymentDate',
  paymentMode: 'paymentMode',
  amount: 'amount',
  particulars: 'particulars',
  originalOutWarrantyId: 'originalOutWarrantyId',
  originalReceiptNo: 'originalReceiptNo',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.Service_reportsScalarFieldEnum = {
  id: 'id',
  customerId: 'customerId',
  executiveId: 'executiveId',
  reportDate: 'reportDate',
  visitDate: 'visitDate',
  completionDate: 'completionDate',
  natureOfService: 'natureOfService',
  complaintType: 'complaintType',
  actionTaken: 'actionTaken',
  remarks: 'remarks',
  status: 'status',
  originalId: 'originalId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.Service_detailsScalarFieldEnum = {
  id: 'id',
  serviceReportId: 'serviceReportId',
  machineType: 'machineType',
  serialNumber: 'serialNumber',
  problem: 'problem',
  solution: 'solution',
  partReplaced: 'partReplaced',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.Service_schedulesScalarFieldEnum = {
  id: 'id',
  serviceReportId: 'serviceReportId',
  scheduledDate: 'scheduledDate',
  technicianId: 'technicianId',
  estimatedDuration: 'estimatedDuration',
  priority: 'priority',
  notes: 'notes',
  status: 'status',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.History_cardsScalarFieldEnum = {
  id: 'id',
  customerId: 'customerId',
  cardNo: 'cardNo',
  source: 'source',
  amcId: 'amcId',
  inWarrantyId: 'inWarrantyId',
  outWarrantyId: 'outWarrantyId',
  toCardNo: 'toCardNo',
  originalId: 'originalId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.History_sectionsScalarFieldEnum = {
  id: 'id',
  historyCardId: 'historyCardId',
  sectionCode: 'sectionCode',
  content: 'content',
  originalCardNo: 'originalCardNo',
  originalSectionCode: 'originalSectionCode',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.Sales_leadsScalarFieldEnum = {
  id: 'id',
  customerId: 'customerId',
  executiveId: 'executiveId',
  leadDate: 'leadDate',
  contactPerson: 'contactPerson',
  contactPhone: 'contactPhone',
  status: 'status',
  prospectPercentage: 'prospectPercentage',
  followUpDate: 'followUpDate',
  nextVisitDate: 'nextVisitDate',
  remarks: 'remarks',
  originalId: 'originalId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.Sales_opportunitiesScalarFieldEnum = {
  id: 'id',
  customerId: 'customerId',
  executiveId: 'executiveId',
  opportunityDate: 'opportunityDate',
  contactPerson: 'contactPerson',
  contactPhone: 'contactPhone',
  status: 'status',
  prospectPercentage: 'prospectPercentage',
  followUpDate: 'followUpDate',
  nextVisitDate: 'nextVisitDate',
  remarks: 'remarks',
  originalId: 'originalId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.Sales_prospectsScalarFieldEnum = {
  id: 'id',
  customerId: 'customerId',
  executiveId: 'executiveId',
  prospectDate: 'prospectDate',
  contactPerson: 'contactPerson',
  contactPhone: 'contactPhone',
  status: 'status',
  prospectPercentage: 'prospectPercentage',
  followUpDate: 'followUpDate',
  nextVisitDate: 'nextVisitDate',
  lostReason: 'lostReason',
  remarks: 'remarks',
  originalId: 'originalId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.Sales_ordersScalarFieldEnum = {
  id: 'id',
  customerId: 'customerId',
  executiveId: 'executiveId',
  orderDate: 'orderDate',
  contactPerson: 'contactPerson',
  contactPhone: 'contactPhone',
  status: 'status',
  deliveryDate: 'deliveryDate',
  actualDeliveryDate: 'actualDeliveryDate',
  amount: 'amount',
  remarks: 'remarks',
  originalId: 'originalId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.UsersScalarFieldEnum = {
  id: 'id',
  name: 'name',
  email: 'email',
  password: 'password',
  role: 'role',
  phone: 'phone',
  designation: 'designation',
  isActive: 'isActive',
  originalId: 'originalId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  emailVerified: 'emailVerified',
  image: 'image'
};

exports.Prisma.PasswordResetTokenScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  token: 'token',
  expires: 'expires',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ServiceVisitTypeScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  originalId: 'originalId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ComplaintTypeScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  originalId: 'originalId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ComplaintNatureTypeScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  originalId: 'originalId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.FailureTypeScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  originalId: 'originalId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.TerritoryScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  originalId: 'originalId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SegmentScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  originalId: 'originalId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.CompetitorScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  originalId: 'originalId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.PriorityTypeScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  originalId: 'originalId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.EnquiryTypeScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  originalId: 'originalId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.DeductionTypeScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  originalId: 'originalId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.DebitDivisionScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  originalId: 'originalId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.AccountDivisionScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  originalId: 'originalId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SparePartScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  originalId: 'originalId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.TaxRateScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  originalId: 'originalId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.TransitDamageTypeScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  originalId: 'originalId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.UserGroupScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  originalId: 'originalId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.UspTypeScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  originalId: 'originalId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.VisitTypeScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  originalId: 'originalId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ServiceCenterScalarFieldEnum = {
  id: 'id',
  name: 'name',
  vendor: 'vendor',
  address: 'address',
  city: 'city',
  state: 'state',
  pincode: 'pincode',
  phone: 'phone',
  email: 'email',
  contactPerson: 'contactPerson',
  active: 'active',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.HistoryRepairScalarFieldEnum = {
  id: 'id',
  historyCardId: 'historyCardId',
  repairDate: 'repairDate',
  description: 'description',
  technicianId: 'technicianId',
  partsReplaced: 'partsReplaced',
  cost: 'cost',
  originalCardNo: 'originalCardNo',
  originalRepairId: 'originalRepairId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.HistoryMaintenanceScalarFieldEnum = {
  id: 'id',
  historyCardId: 'historyCardId',
  maintenanceDate: 'maintenanceDate',
  description: 'description',
  technicianId: 'technicianId',
  maintenanceType: 'maintenanceType',
  originalCardNo: 'originalCardNo',
  originalMaintenanceId: 'originalMaintenanceId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.HistoryWaterWashScalarFieldEnum = {
  id: 'id',
  historyCardId: 'historyCardId',
  washDate: 'washDate',
  description: 'description',
  technicianId: 'technicianId',
  originalCardNo: 'originalCardNo',
  originalWashId: 'originalWashId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.HistoryAmcDetailScalarFieldEnum = {
  id: 'id',
  historyCardId: 'historyCardId',
  amcContractId: 'amcContractId',
  startDate: 'startDate',
  endDate: 'endDate',
  amount: 'amount',
  description: 'description',
  originalCardNo: 'originalCardNo',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  originalAmcDetailId: 'originalAmcDetailId'
};

exports.Prisma.HistoryAddonDetailScalarFieldEnum = {
  id: 'id',
  historyCardId: 'historyCardId',
  addonDate: 'addonDate',
  description: 'description',
  amount: 'amount',
  technicianId: 'technicianId',
  originalCardNo: 'originalCardNo',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  originalAddonDetailId: 'originalAddonDetailId'
};

exports.Prisma.HistoryAuditScalarFieldEnum = {
  id: 'id',
  historyCardId: 'historyCardId',
  auditDate: 'auditDate',
  description: 'description',
  auditorId: 'auditorId',
  findings: 'findings',
  recommendations: 'recommendations',
  originalCardNo: 'originalCardNo',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  originalAuditId: 'originalAuditId'
};

exports.Prisma.HistoryComplaintScalarFieldEnum = {
  id: 'id',
  historyCardId: 'historyCardId',
  complaintDate: 'complaintDate',
  description: 'description',
  complaintType: 'complaintType',
  resolution: 'resolution',
  resolutionDate: 'resolutionDate',
  originalCardNo: 'originalCardNo',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  originalComplaintId: 'originalComplaintId'
};

exports.Prisma.HistoryComponentReplacementScalarFieldEnum = {
  id: 'id',
  historyCardId: 'historyCardId',
  replacementDate: 'replacementDate',
  componentName: 'componentName',
  serialNumber: 'serialNumber',
  reason: 'reason',
  technicianId: 'technicianId',
  cost: 'cost',
  originalCardNo: 'originalCardNo',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  originalReplacementId: 'originalReplacementId'
};

exports.Prisma.EmailTemplateScalarFieldEnum = {
  id: 'id',
  name: 'name',
  subject: 'subject',
  bodyHtml: 'bodyHtml',
  bodyText: 'bodyText',
  description: 'description',
  variables: 'variables',
  category: 'category',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.NotificationPreferenceScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  salesLeadCreated: 'salesLeadCreated',
  salesLeadStatusChanged: 'salesLeadStatusChanged',
  salesOpportunityCreated: 'salesOpportunityCreated',
  salesOpportunityStatusChanged: 'salesOpportunityStatusChanged',
  salesProspectCreated: 'salesProspectCreated',
  salesProspectStatusChanged: 'salesProspectStatusChanged',
  salesOrderCreated: 'salesOrderCreated',
  salesOrderStatusChanged: 'salesOrderStatusChanged',
  salesConversionEvents: 'salesConversionEvents',
  dailySalesSummary: 'dailySalesSummary',
  weeklySalesReport: 'weeklySalesReport',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SalesNotificationEventScalarFieldEnum = {
  id: 'id',
  eventType: 'eventType',
  entityType: 'entityType',
  entityId: 'entityId',
  userId: 'userId',
  customerId: 'customerId',
  executiveId: 'executiveId',
  oldStatus: 'oldStatus',
  newStatus: 'newStatus',
  eventData: 'eventData',
  processed: 'processed',
  processedAt: 'processedAt',
  createdAt: 'createdAt'
};

exports.Prisma.SalesNotificationQueueScalarFieldEnum = {
  id: 'id',
  eventId: 'eventId',
  recipientUserId: 'recipientUserId',
  recipientEmail: 'recipientEmail',
  templateName: 'templateName',
  templateData: 'templateData',
  priority: 'priority',
  status: 'status',
  attempts: 'attempts',
  maxAttempts: 'maxAttempts',
  lastAttemptAt: 'lastAttemptAt',
  sentAt: 'sentAt',
  failureReason: 'failureReason',
  emailLogId: 'emailLogId',
  scheduledFor: 'scheduledFor',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.QuotationsScalarFieldEnum = {
  id: 'id',
  quotationNumber: 'quotationNumber',
  customerId: 'customerId',
  executiveId: 'executiveId',
  quotationDate: 'quotationDate',
  validUntil: 'validUntil',
  status: 'status',
  contactPerson: 'contactPerson',
  contactPhone: 'contactPhone',
  contactEmail: 'contactEmail',
  subject: 'subject',
  notes: 'notes',
  termsConditions: 'termsConditions',
  subtotal: 'subtotal',
  taxAmount: 'taxAmount',
  totalAmount: 'totalAmount',
  discount: 'discount',
  discountType: 'discountType',
  originalId: 'originalId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.Quotation_itemsScalarFieldEnum = {
  id: 'id',
  quotationId: 'quotationId',
  productId: 'productId',
  modelId: 'modelId',
  brandId: 'brandId',
  description: 'description',
  quantity: 'quantity',
  unitPrice: 'unitPrice',
  totalPrice: 'totalPrice',
  taxRate: 'taxRate',
  taxAmount: 'taxAmount',
  discount: 'discount',
  discountType: 'discountType',
  specifications: 'specifications',
  notes: 'notes',
  sortOrder: 'sortOrder',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.EmailLogScalarFieldEnum = {
  id: 'id',
  templateId: 'templateId',
  recipient: 'recipient',
  subject: 'subject',
  bodyHtml: 'bodyHtml',
  bodyText: 'bodyText',
  cc: 'cc',
  bcc: 'bcc',
  status: 'status',
  errorMessage: 'errorMessage',
  sentAt: 'sentAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.Activity_logsScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  action: 'action',
  entityType: 'entityType',
  entityId: 'entityId',
  details: 'details',
  ipAddress: 'ipAddress',
  userAgent: 'userAgent',
  createdAt: 'createdAt'
};

exports.Prisma.AccountScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  type: 'type',
  provider: 'provider',
  providerAccountId: 'providerAccountId',
  refresh_token: 'refresh_token',
  access_token: 'access_token',
  expires_at: 'expires_at',
  token_type: 'token_type',
  scope: 'scope',
  id_token: 'id_token',
  session_state: 'session_state'
};

exports.Prisma.SessionScalarFieldEnum = {
  id: 'id',
  sessionToken: 'sessionToken',
  userId: 'userId',
  expires: 'expires'
};

exports.Prisma.VerificationTokenScalarFieldEnum = {
  identifier: 'identifier',
  token: 'token',
  expires: 'expires'
};

exports.Prisma.SpareTypeScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  code: 'code',
  active: 'active',
  originalId: 'originalId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.MeasurementTypeScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  code: 'code',
  active: 'active',
  originalId: 'originalId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.Scheduled_reportsScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  reportType: 'reportType',
  cronExpression: 'cronExpression',
  isActive: 'isActive',
  parameters: 'parameters',
  emailRecipients: 'emailRecipients',
  emailSubject: 'emailSubject',
  emailBody: 'emailBody',
  exportFormat: 'exportFormat',
  createdBy: 'createdBy',
  lastRunAt: 'lastRunAt',
  nextRunAt: 'nextRunAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.Scheduled_report_executionsScalarFieldEnum = {
  id: 'id',
  scheduledReportId: 'scheduledReportId',
  status: 'status',
  startedAt: 'startedAt',
  completedAt: 'completedAt',
  errorMessage: 'errorMessage',
  reportData: 'reportData',
  filePath: 'filePath',
  emailsSent: 'emailsSent',
  emailErrors: 'emailErrors',
  executionTime: 'executionTime',
  recordCount: 'recordCount',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.Report_formulasScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  formula: 'formula',
  category: 'category',
  isTemplate: 'isTemplate',
  isActive: 'isActive',
  variables: 'variables',
  returnType: 'returnType',
  complexity: 'complexity',
  usageCount: 'usageCount',
  createdBy: 'createdBy',
  lastUsedAt: 'lastUsedAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.Report_formula_fieldsScalarFieldEnum = {
  id: 'id',
  reportType: 'reportType',
  fieldName: 'fieldName',
  fieldLabel: 'fieldLabel',
  formulaId: 'formulaId',
  isActive: 'isActive',
  sortOrder: 'sortOrder',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.Formula_test_casesScalarFieldEnum = {
  id: 'id',
  formulaId: 'formulaId',
  name: 'name',
  description: 'description',
  inputData: 'inputData',
  expectedResult: 'expectedResult',
  actualResult: 'actualResult',
  passed: 'passed',
  lastRunAt: 'lastRunAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.NullableJsonNullValueInput = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull
};

exports.Prisma.JsonNullValueInput = {
  JsonNull: Prisma.JsonNull
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};

exports.Prisma.UserOrderByRelevanceFieldEnum = {
  name: 'name',
  Phone: 'Phone',
  Desig: 'Desig',
  DescDesig: 'DescDesig'
};

exports.Prisma.LegacyCustomerOrderByRelevanceFieldEnum = {
  name: 'name',
  address: 'address',
  location: 'location',
  phone1: 'phone1',
  phone2: 'phone2',
  phone3: 'phone3',
  mobile: 'mobile',
  fax: 'fax',
  email: 'email',
  birthDate: 'birthDate',
  anniversaryDate: 'anniversaryDate',
  birthYear: 'birthYear',
  anniversaryYear: 'anniversaryYear',
  designation: 'designation',
  visitCardPath: 'visitCardPath'
};

exports.Prisma.CustomerOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  address: 'address',
  city: 'city',
  state: 'state',
  pinCode: 'pinCode',
  phone: 'phone',
  fax: 'fax',
  email: 'email',
  mobile: 'mobile',
  website: 'website',
  phone1: 'phone1',
  phone2: 'phone2',
  phone3: 'phone3',
  location: 'location',
  birthYear: 'birthYear',
  anniversaryYear: 'anniversaryYear',
  designation: 'designation',
  visitCardPath: 'visitCardPath'
};

exports.Prisma.ContactOrderByRelevanceFieldEnum = {
  id: 'id',
  customerId: 'customerId',
  name: 'name',
  designation: 'designation',
  phone: 'phone',
  email: 'email'
};

exports.Prisma.VisitCardOrderByRelevanceFieldEnum = {
  id: 'id',
  customerId: 'customerId',
  filePath: 'filePath',
  notes: 'notes'
};

exports.Prisma.BrandOrderByRelevanceFieldEnum = {
  name: 'name'
};

exports.Prisma.brandsOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description'
};

exports.Prisma.ProductOrderByRelevanceFieldEnum = {
  name: 'name'
};

exports.Prisma.productsOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  brandId: 'brandId'
};

exports.Prisma.ModelOrderByRelevanceFieldEnum = {
  name: 'name',
  specs: 'specs'
};

exports.Prisma.modelsOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  specs: 'specs',
  productId: 'productId'
};

exports.Prisma.AMCContractOrderByRelevanceFieldEnum = {
  contactPerson: 'contactPerson',
  contactPhone: 'contactPhone',
  natureOfService: 'natureOfService',
  renewalFlag: 'renewalFlag',
  blstrFlag: 'blstrFlag',
  fresh: 'fresh',
  paymentMode: 'paymentMode',
  category: 'category'
};

exports.Prisma.amc_contractsOrderByRelevanceFieldEnum = {
  id: 'id',
  customerId: 'customerId',
  executiveId: 'executiveId',
  contactPersonId: 'contactPersonId',
  natureOfService: 'natureOfService',
  blstrFlag: 'blstrFlag',
  fresh: 'fresh',
  paymentMode: 'paymentMode',
  category: 'category',
  status: 'status',
  usersId: 'usersId'
};

exports.Prisma.AMCMachineOrderByRelevanceFieldEnum = {
  location: 'location',
  LoctFlag: 'LoctFlag',
  serialNumber: 'serialNumber',
  Section: 'Section'
};

exports.Prisma.amc_machinesOrderByRelevanceFieldEnum = {
  id: 'id',
  amcContractId: 'amcContractId',
  productId: 'productId',
  modelId: 'modelId',
  brandId: 'brandId',
  location: 'location',
  locationFlag: 'locationFlag',
  serialNumber: 'serialNumber',
  section: 'section'
};

exports.Prisma.AMCComponentOrderByRelevanceFieldEnum = {
  serialNumber: 'serialNumber',
  Section: 'Section'
};

exports.Prisma.amc_componentsOrderByRelevanceFieldEnum = {
  id: 'id',
  machineId: 'machineId',
  serialNumber: 'serialNumber',
  section: 'section'
};

exports.Prisma.amc_paymentsOrderByRelevanceFieldEnum = {
  id: 'id',
  amcContractId: 'amcContractId',
  receiptNo: 'receiptNo',
  paymentMode: 'paymentMode',
  particulars: 'particulars'
};

exports.Prisma.AMCServiceDateOrderByRelevanceFieldEnum = {
  serviceFlag: 'serviceFlag'
};

exports.Prisma.amc_service_datesOrderByRelevanceFieldEnum = {
  id: 'id',
  amcContractId: 'amcContractId',
  serviceFlag: 'serviceFlag'
};

exports.Prisma.DivisionOrderByRelevanceFieldEnum = {
  name: 'name'
};

exports.Prisma.divisionsOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description'
};

exports.Prisma.amc_divisionsOrderByRelevanceFieldEnum = {
  id: 'id',
  amcContractId: 'amcContractId',
  divisionId: 'divisionId'
};

exports.Prisma.InWarrantyOrderByRelevanceFieldEnum = {
  contactPerson: 'contactPerson',
  contactPhone: 'contactPhone',
  bslNo: 'bslNo'
};

exports.Prisma.warrantiesOrderByRelevanceFieldEnum = {
  id: 'id',
  customerId: 'customerId',
  executiveId: 'executiveId',
  contactPersonId: 'contactPersonId',
  bslNo: 'bslNo',
  technicianId: 'technicianId',
  amcId: 'amcId',
  status: 'status',
  usersId: 'usersId'
};

exports.Prisma.InWarrantyMachineOrderByRelevanceFieldEnum = {
  location: 'location',
  locationFlag: 'locationFlag',
  serialNumber: 'serialNumber',
  section: 'section'
};

exports.Prisma.warranty_machinesOrderByRelevanceFieldEnum = {
  id: 'id',
  warrantyId: 'warrantyId',
  productId: 'productId',
  modelId: 'modelId',
  brandId: 'brandId',
  location: 'location',
  locationFlag: 'locationFlag',
  serialNumber: 'serialNumber',
  section: 'section'
};

exports.Prisma.InWarrantyComponentOrderByRelevanceFieldEnum = {
  serialNumber: 'serialNumber',
  section: 'section'
};

exports.Prisma.warranty_componentsOrderByRelevanceFieldEnum = {
  id: 'id',
  machineId: 'machineId',
  serialNumber: 'serialNumber',
  section: 'section'
};

exports.Prisma.ACC_DIVISIONOrderByRelevanceFieldEnum = {
  Name: 'Name'
};

exports.Prisma.AMC_AddOnOrderByRelevanceFieldEnum = {
  Loct: 'Loct',
  LoctFlag: 'LoctFlag',
  MCSlNo: 'MCSlNo',
  Section: 'Section'
};

exports.Prisma.AMC_AddOnCompOrderByRelevanceFieldEnum = {
  CompSlNo: 'CompSlNo',
  Section: 'Section'
};

exports.Prisma.AMC_NewLoctOrderByRelevanceFieldEnum = {
  NewLoct: 'NewLoct'
};

exports.Prisma.BluestarAccounts_SumOrderByRelevanceFieldEnum = {
  Mode: 'Mode',
  BslExec: 'BslExec'
};

exports.Prisma.CATEGORY_SRVOrderByRelevanceFieldEnum = {
  Name: 'Name'
};

exports.Prisma.COMPETITOROrderByRelevanceFieldEnum = {
  Name: 'Name'
};

exports.Prisma.COMPLAINTOrderByRelevanceFieldEnum = {
  Name: 'Name'
};

exports.Prisma.CRMOrderByRelevanceFieldEnum = {
  CustST: 'CustST',
  CPer: 'CPer',
  SrvRestrict: 'SrvRestrict',
  StLevel: 'StLevel',
  Place: 'Place',
  Relation: 'Relation',
  USP: 'USP',
  BRelation: 'BRelation'
};

exports.Prisma.Cust_FreshOrderByRelevanceFieldEnum = {
  Name: 'Name',
  CustAdd: 'CustAdd',
  CustPhone1: 'CustPhone1',
  CustPhone2: 'CustPhone2',
  CustFax: 'CustFax',
  CustEmail: 'CustEmail'
};

exports.Prisma.DEBIT_DIVOrderByRelevanceFieldEnum = {
  DebitDiv: 'DebitDiv'
};

exports.Prisma.DEDUCTION_HEADOrderByRelevanceFieldEnum = {
  Head: 'Head'
};

exports.Prisma.ENQUIRYOrderByRelevanceFieldEnum = {
  Name: 'Name'
};

exports.Prisma.EnqCompOrderByRelevanceFieldEnum = {
  CTime: 'CTime',
  CustST: 'CustST',
  Status: 'Status',
  Remarks: 'Remarks',
  CallType: 'CallType',
  PFlag: 'PFlag'
};

exports.Prisma.FAILUREOrderByRelevanceFieldEnum = {
  Name: 'Name'
};

exports.Prisma.Funnel_sumOrderByRelevanceFieldEnum = {
  TStatus: 'TStatus',
  Prospect: 'Prospect',
  OrdST: 'OrdST',
  LostReason: 'LostReason',
  CPerson: 'CPerson',
  Phone: 'Phone',
  Remarks: 'Remarks'
};

exports.Prisma.Gateway_sumOrderByRelevanceFieldEnum = {
  TStatus: 'TStatus',
  Prospect: 'Prospect',
  OrdST: 'OrdST',
  LostReason: 'LostReason',
  CPerson: 'CPerson',
  Phone: 'Phone',
  Remarks: 'Remarks'
};

exports.Prisma.HistorySectionOrderByRelevanceFieldEnum = {
  Section: 'Section'
};

exports.Prisma.History_AddOnDetOrderByRelevanceFieldEnum = {
  WarnNo: 'WarnNo'
};

exports.Prisma.History_AmcDetOrderByRelevanceFieldEnum = {
  BlkYear: 'BlkYear'
};

exports.Prisma.History_SumOrderByRelevanceFieldEnum = {
  CPerson: 'CPerson',
  Phone: 'Phone',
  AddonFlag: 'AddonFlag',
  Source: 'Source'
};

exports.Prisma.InwInst_ParamOrderByRelevanceFieldEnum = {
  SP: 'SP',
  DP: 'DP',
  AMP: 'AMP',
  VOLT: 'VOLT',
  CFM: 'CFM',
  Noise: 'Noise',
  RTemp: 'RTemp',
  ATemp: 'ATemp'
};

exports.Prisma.Inwarranty_NewLoctOrderByRelevanceFieldEnum = {
  NewLoct: 'NewLoct'
};

exports.Prisma.Inwarranty_SerDatesOrderByRelevanceFieldEnum = {
  SerFlag: 'SerFlag'
};

exports.Prisma.MovementRegister_ProductsOrderByRelevanceFieldEnum = {
  MacSlno: 'MacSlno',
  CompSlNo: 'CompSlNo',
  PartName: 'PartName',
  PartSlNo: 'PartSlNo',
  ReceiptNo: 'ReceiptNo'
};

exports.Prisma.NATURE_SERVICEOrderByRelevanceFieldEnum = {
  Name: 'Name'
};

exports.Prisma.NAT_COMPLAINTOrderByRelevanceFieldEnum = {
  Name: 'Name'
};

exports.Prisma.ORG_DIVOrderByRelevanceFieldEnum = {
  OrgDiv: 'OrgDiv'
};

exports.Prisma.OrderFormOrderByRelevanceFieldEnum = {
  ToAdd: 'ToAdd',
  Attn: 'Attn',
  OrdPlace: 'OrdPlace',
  CustOrdNo: 'CustOrdNo',
  CustAdd: 'CustAdd',
  Payment: 'Payment',
  DelPlace: 'DelPlace',
  WorstDel: 'WorstDel'
};

exports.Prisma.Order_payOrderByRelevanceFieldEnum = {
  Mode: 'Mode',
  Particulars: 'Particulars',
  BlstrFlg: 'BlstrFlg'
};

exports.Prisma.Order_sumOrderByRelevanceFieldEnum = {
  TStatus: 'TStatus',
  TaplNo: 'TaplNo',
  BslInvNo: 'BslInvNo',
  FrmWhere: 'FrmWhere',
  Prospect: 'Prospect',
  DelAdd: 'DelAdd',
  InstaChgsTo: 'InstaChgsTo',
  CPerson: 'CPerson',
  Phone: 'Phone',
  Remarks: 'Remarks'
};

exports.Prisma.OutWnty_CompOrderByRelevanceFieldEnum = {
  CompSlNo: 'CompSlNo',
  Section: 'Section'
};

exports.Prisma.OutWnty_MachineOrderByRelevanceFieldEnum = {
  Loct: 'Loct',
  MCSlNo: 'MCSlNo',
  Section: 'Section'
};

exports.Prisma.OutWnty_sumOrderByRelevanceFieldEnum = {
  Cperson: 'Cperson',
  Phone: 'Phone',
  Source: 'Source'
};

exports.Prisma.PRIORITY_LOIOrderByRelevanceFieldEnum = {
  Name: 'Name'
};

exports.Prisma.PROFORMA_MODELOrderByRelevanceFieldEnum = {
  Name: 'Name',
  Description: 'Description'
};

exports.Prisma.Perf_Inv_SumOrderByRelevanceFieldEnum = {
  TStatus: 'TStatus',
  RefNo: 'RefNo',
  RefText: 'RefText',
  OrderBsl: 'OrderBsl',
  OrderTapl: 'OrderTapl'
};

exports.Prisma.Pipeline_sumOrderByRelevanceFieldEnum = {
  TStatus: 'TStatus',
  Prospect: 'Prospect',
  OrdST: 'OrdST',
  LostReason: 'LostReason',
  CPerson: 'CPerson',
  Phone: 'Phone',
  Remarks: 'Remarks'
};

exports.Prisma.Proforma_SumOrderByRelevanceFieldEnum = {
  RefNo: 'RefNo',
  OrderBsl: 'OrderBsl',
  OrderTapl: 'OrderTapl',
  Remarks: 'Remarks',
  Status: 'Status',
  DelAddr: 'DelAddr',
  Greetings: 'Greetings'
};

exports.Prisma.Rel_StatisticsOrderByRelevanceFieldEnum = {
  CustST: 'CustST',
  CPer: 'CPer',
  Phone: 'Phone',
  FeedBack: 'FeedBack'
};

exports.Prisma.Rpt_AmcTmpOrderByRelevanceFieldEnum = {
  MCSlNo: 'MCSlNo',
  CompSlNo: 'CompSlNo',
  Location: 'Location'
};

exports.Prisma.SEGMENTOrderByRelevanceFieldEnum = {
  Name: 'Name'
};

exports.Prisma.SER_VISITOrderByRelevanceFieldEnum = {
  SerVisit: 'SerVisit'
};

exports.Prisma.SOMSOrderByRelevanceFieldEnum = {
  CustST: 'CustST',
  CPer: 'CPer',
  CPhone: 'CPhone',
  DownTime: 'DownTime',
  FaultRpt: 'FaultRpt',
  PartFailure: 'PartFailure',
  UpTime: 'UpTime',
  Rect: 'Rect',
  StLevel: 'StLevel',
  TAT: 'TAT',
  StMac: 'StMac',
  Remarks: 'Remarks'
};

exports.Prisma.SPAREOrderByRelevanceFieldEnum = {
  Name: 'Name'
};

exports.Prisma.ServiceRpt_FailureOrderByRelevanceFieldEnum = {
  Remedy: 'Remedy'
};

exports.Prisma.ServiceRpt_MeasureOrderByRelevanceFieldEnum = {
  SP: 'SP',
  DP: 'DP',
  Amp: 'Amp',
  Volt: 'Volt',
  CFM: 'CFM',
  Noise: 'Noise',
  RTemp: 'RTemp',
  ATemp: 'ATemp',
  TimeOn: 'TimeOn',
  GCT: 'GCT'
};

exports.Prisma.ServiceRpt_sumOrderByRelevanceFieldEnum = {
  Item: 'Item',
  Fault: 'Fault',
  Action: 'Action',
  FollowUp: 'FollowUp'
};

exports.Prisma.SpareCostOrderByRelevanceFieldEnum = {
  PurNo: 'PurNo'
};

exports.Prisma.TERRITORYOrderByRelevanceFieldEnum = {
  Terr: 'Terr'
};

exports.Prisma.TRANSIT_DAMAGEOrderByRelevanceFieldEnum = {
  TrDamage: 'TrDamage'
};

exports.Prisma.TransactionOrderByRelevanceFieldEnum = {
  TStatus: 'TStatus',
  STFlag: 'STFlag',
  CustSt: 'CustSt',
  Ideac: 'Ideac'
};

exports.Prisma.Transaction_MachineOrderByRelevanceFieldEnum = {
  BlStar: 'BlStar'
};

exports.Prisma.USERGROUPOrderByRelevanceFieldEnum = {
  Name: 'Name'
};

exports.Prisma.USPOrderByRelevanceFieldEnum = {
  Name: 'Name'
};

exports.Prisma.UserPwdOrderByRelevanceFieldEnum = {
  User: 'User',
  Pwd: 'Pwd',
  Key: 'Key'
};

exports.Prisma.VISITOrderByRelevanceFieldEnum = {
  Name: 'Name'
};

exports.Prisma.out_warrantiesOrderByRelevanceFieldEnum = {
  id: 'id',
  customerId: 'customerId',
  executiveId: 'executiveId',
  contactPerson: 'contactPerson',
  contactPhone: 'contactPhone',
  source: 'source',
  sourceId: 'sourceId',
  amcSourceId: 'amcSourceId',
  inWarrantySourceId: 'inWarrantySourceId'
};

exports.Prisma.out_warranty_machinesOrderByRelevanceFieldEnum = {
  id: 'id',
  outWarrantyId: 'outWarrantyId',
  modelId: 'modelId',
  serialNumber: 'serialNumber',
  location: 'location'
};

exports.Prisma.out_warranty_componentsOrderByRelevanceFieldEnum = {
  id: 'id',
  machineId: 'machineId',
  type: 'type',
  serialNumber: 'serialNumber',
  out_warrantiesId: 'out_warrantiesId'
};

exports.Prisma.out_warranty_paymentsOrderByRelevanceFieldEnum = {
  id: 'id',
  outWarrantyId: 'outWarrantyId',
  receiptNo: 'receiptNo',
  paymentMode: 'paymentMode',
  particulars: 'particulars'
};

exports.Prisma.service_reportsOrderByRelevanceFieldEnum = {
  id: 'id',
  customerId: 'customerId',
  executiveId: 'executiveId',
  natureOfService: 'natureOfService',
  complaintType: 'complaintType',
  actionTaken: 'actionTaken',
  remarks: 'remarks',
  status: 'status'
};

exports.Prisma.service_detailsOrderByRelevanceFieldEnum = {
  id: 'id',
  serviceReportId: 'serviceReportId',
  machineType: 'machineType',
  serialNumber: 'serialNumber',
  problem: 'problem',
  solution: 'solution',
  partReplaced: 'partReplaced'
};

exports.Prisma.service_schedulesOrderByRelevanceFieldEnum = {
  id: 'id',
  serviceReportId: 'serviceReportId',
  technicianId: 'technicianId',
  priority: 'priority',
  notes: 'notes',
  status: 'status'
};

exports.Prisma.history_cardsOrderByRelevanceFieldEnum = {
  id: 'id',
  customerId: 'customerId',
  source: 'source',
  amcId: 'amcId',
  inWarrantyId: 'inWarrantyId',
  outWarrantyId: 'outWarrantyId'
};

exports.Prisma.history_sectionsOrderByRelevanceFieldEnum = {
  id: 'id',
  historyCardId: 'historyCardId',
  sectionCode: 'sectionCode',
  content: 'content',
  originalSectionCode: 'originalSectionCode'
};

exports.Prisma.sales_leadsOrderByRelevanceFieldEnum = {
  id: 'id',
  customerId: 'customerId',
  executiveId: 'executiveId',
  contactPerson: 'contactPerson',
  contactPhone: 'contactPhone',
  status: 'status',
  remarks: 'remarks'
};

exports.Prisma.sales_opportunitiesOrderByRelevanceFieldEnum = {
  id: 'id',
  customerId: 'customerId',
  executiveId: 'executiveId',
  contactPerson: 'contactPerson',
  contactPhone: 'contactPhone',
  status: 'status',
  remarks: 'remarks'
};

exports.Prisma.sales_prospectsOrderByRelevanceFieldEnum = {
  id: 'id',
  customerId: 'customerId',
  executiveId: 'executiveId',
  contactPerson: 'contactPerson',
  contactPhone: 'contactPhone',
  status: 'status',
  lostReason: 'lostReason',
  remarks: 'remarks'
};

exports.Prisma.sales_ordersOrderByRelevanceFieldEnum = {
  id: 'id',
  customerId: 'customerId',
  executiveId: 'executiveId',
  contactPerson: 'contactPerson',
  contactPhone: 'contactPhone',
  status: 'status',
  remarks: 'remarks'
};

exports.Prisma.usersOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  email: 'email',
  password: 'password',
  role: 'role',
  phone: 'phone',
  designation: 'designation',
  image: 'image'
};

exports.Prisma.PasswordResetTokenOrderByRelevanceFieldEnum = {
  id: 'id',
  userId: 'userId',
  token: 'token'
};

exports.Prisma.ServiceVisitTypeOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description'
};

exports.Prisma.ComplaintTypeOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description'
};

exports.Prisma.ComplaintNatureTypeOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description'
};

exports.Prisma.FailureTypeOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description'
};

exports.Prisma.TerritoryOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description'
};

exports.Prisma.SegmentOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description'
};

exports.Prisma.CompetitorOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description'
};

exports.Prisma.PriorityTypeOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description'
};

exports.Prisma.EnquiryTypeOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description'
};

exports.Prisma.DeductionTypeOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description'
};

exports.Prisma.DebitDivisionOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description'
};

exports.Prisma.AccountDivisionOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description'
};

exports.Prisma.SparePartOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description'
};

exports.Prisma.TaxRateOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description'
};

exports.Prisma.TransitDamageTypeOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description'
};

exports.Prisma.UserGroupOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description'
};

exports.Prisma.UspTypeOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description'
};

exports.Prisma.VisitTypeOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description'
};

exports.Prisma.ServiceCenterOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  vendor: 'vendor',
  address: 'address',
  city: 'city',
  state: 'state',
  pincode: 'pincode',
  phone: 'phone',
  email: 'email',
  contactPerson: 'contactPerson'
};

exports.Prisma.HistoryRepairOrderByRelevanceFieldEnum = {
  id: 'id',
  historyCardId: 'historyCardId',
  description: 'description',
  technicianId: 'technicianId',
  partsReplaced: 'partsReplaced'
};

exports.Prisma.HistoryMaintenanceOrderByRelevanceFieldEnum = {
  id: 'id',
  historyCardId: 'historyCardId',
  description: 'description',
  technicianId: 'technicianId',
  maintenanceType: 'maintenanceType'
};

exports.Prisma.HistoryWaterWashOrderByRelevanceFieldEnum = {
  id: 'id',
  historyCardId: 'historyCardId',
  description: 'description',
  technicianId: 'technicianId'
};

exports.Prisma.HistoryAmcDetailOrderByRelevanceFieldEnum = {
  id: 'id',
  historyCardId: 'historyCardId',
  amcContractId: 'amcContractId',
  description: 'description'
};

exports.Prisma.HistoryAddonDetailOrderByRelevanceFieldEnum = {
  id: 'id',
  historyCardId: 'historyCardId',
  description: 'description',
  technicianId: 'technicianId'
};

exports.Prisma.HistoryAuditOrderByRelevanceFieldEnum = {
  id: 'id',
  historyCardId: 'historyCardId',
  description: 'description',
  auditorId: 'auditorId',
  findings: 'findings',
  recommendations: 'recommendations'
};

exports.Prisma.HistoryComplaintOrderByRelevanceFieldEnum = {
  id: 'id',
  historyCardId: 'historyCardId',
  description: 'description',
  complaintType: 'complaintType',
  resolution: 'resolution'
};

exports.Prisma.HistoryComponentReplacementOrderByRelevanceFieldEnum = {
  id: 'id',
  historyCardId: 'historyCardId',
  componentName: 'componentName',
  serialNumber: 'serialNumber',
  reason: 'reason',
  technicianId: 'technicianId'
};

exports.Prisma.EmailTemplateOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  subject: 'subject',
  bodyHtml: 'bodyHtml',
  bodyText: 'bodyText',
  description: 'description',
  variables: 'variables',
  category: 'category'
};

exports.Prisma.NotificationPreferenceOrderByRelevanceFieldEnum = {
  id: 'id',
  userId: 'userId'
};

exports.Prisma.JsonNullValueFilter = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull,
  AnyNull: Prisma.AnyNull
};

exports.Prisma.SalesNotificationEventOrderByRelevanceFieldEnum = {
  id: 'id',
  eventType: 'eventType',
  entityType: 'entityType',
  entityId: 'entityId',
  userId: 'userId',
  customerId: 'customerId',
  executiveId: 'executiveId',
  oldStatus: 'oldStatus',
  newStatus: 'newStatus'
};

exports.Prisma.SalesNotificationQueueOrderByRelevanceFieldEnum = {
  id: 'id',
  eventId: 'eventId',
  recipientUserId: 'recipientUserId',
  recipientEmail: 'recipientEmail',
  templateName: 'templateName',
  priority: 'priority',
  status: 'status',
  failureReason: 'failureReason',
  emailLogId: 'emailLogId'
};

exports.Prisma.quotationsOrderByRelevanceFieldEnum = {
  id: 'id',
  quotationNumber: 'quotationNumber',
  customerId: 'customerId',
  executiveId: 'executiveId',
  status: 'status',
  contactPerson: 'contactPerson',
  contactPhone: 'contactPhone',
  contactEmail: 'contactEmail',
  subject: 'subject',
  notes: 'notes',
  termsConditions: 'termsConditions',
  discountType: 'discountType'
};

exports.Prisma.quotation_itemsOrderByRelevanceFieldEnum = {
  id: 'id',
  quotationId: 'quotationId',
  productId: 'productId',
  modelId: 'modelId',
  brandId: 'brandId',
  description: 'description',
  discountType: 'discountType',
  specifications: 'specifications',
  notes: 'notes'
};

exports.Prisma.EmailLogOrderByRelevanceFieldEnum = {
  id: 'id',
  templateId: 'templateId',
  recipient: 'recipient',
  subject: 'subject',
  bodyHtml: 'bodyHtml',
  bodyText: 'bodyText',
  cc: 'cc',
  bcc: 'bcc',
  status: 'status',
  errorMessage: 'errorMessage'
};

exports.Prisma.activity_logsOrderByRelevanceFieldEnum = {
  id: 'id',
  userId: 'userId',
  action: 'action',
  entityType: 'entityType',
  entityId: 'entityId',
  ipAddress: 'ipAddress',
  userAgent: 'userAgent'
};

exports.Prisma.AccountOrderByRelevanceFieldEnum = {
  id: 'id',
  userId: 'userId',
  type: 'type',
  provider: 'provider',
  providerAccountId: 'providerAccountId',
  refresh_token: 'refresh_token',
  access_token: 'access_token',
  token_type: 'token_type',
  scope: 'scope',
  id_token: 'id_token',
  session_state: 'session_state'
};

exports.Prisma.SessionOrderByRelevanceFieldEnum = {
  id: 'id',
  sessionToken: 'sessionToken',
  userId: 'userId'
};

exports.Prisma.VerificationTokenOrderByRelevanceFieldEnum = {
  identifier: 'identifier',
  token: 'token'
};

exports.Prisma.SpareTypeOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  code: 'code'
};

exports.Prisma.MeasurementTypeOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  code: 'code'
};

exports.Prisma.scheduled_reportsOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  reportType: 'reportType',
  cronExpression: 'cronExpression',
  emailRecipients: 'emailRecipients',
  emailSubject: 'emailSubject',
  emailBody: 'emailBody',
  exportFormat: 'exportFormat',
  createdBy: 'createdBy'
};

exports.Prisma.scheduled_report_executionsOrderByRelevanceFieldEnum = {
  id: 'id',
  scheduledReportId: 'scheduledReportId',
  status: 'status',
  errorMessage: 'errorMessage',
  filePath: 'filePath',
  emailErrors: 'emailErrors'
};

exports.Prisma.report_formulasOrderByRelevanceFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  formula: 'formula',
  category: 'category',
  returnType: 'returnType',
  createdBy: 'createdBy'
};

exports.Prisma.report_formula_fieldsOrderByRelevanceFieldEnum = {
  id: 'id',
  reportType: 'reportType',
  fieldName: 'fieldName',
  fieldLabel: 'fieldLabel',
  formulaId: 'formulaId'
};

exports.Prisma.formula_test_casesOrderByRelevanceFieldEnum = {
  id: 'id',
  formulaId: 'formulaId',
  name: 'name',
  description: 'description'
};


exports.Prisma.ModelName = {
  User: 'User',
  LegacyCustomer: 'LegacyCustomer',
  Customer: 'Customer',
  Contact: 'Contact',
  VisitCard: 'VisitCard',
  Brand: 'Brand',
  brands: 'brands',
  Product: 'Product',
  products: 'products',
  Model: 'Model',
  models: 'models',
  AMCContract: 'AMCContract',
  amc_contracts: 'amc_contracts',
  AMCMachine: 'AMCMachine',
  amc_machines: 'amc_machines',
  AMCComponent: 'AMCComponent',
  amc_components: 'amc_components',
  amc_payments: 'amc_payments',
  AMCServiceDate: 'AMCServiceDate',
  amc_service_dates: 'amc_service_dates',
  Division: 'Division',
  divisions: 'divisions',
  AMCDivision: 'AMCDivision',
  amc_divisions: 'amc_divisions',
  InWarranty: 'InWarranty',
  warranties: 'warranties',
  InWarrantyMachine: 'InWarrantyMachine',
  warranty_machines: 'warranty_machines',
  InWarrantyComponent: 'InWarrantyComponent',
  warranty_components: 'warranty_components',
  ACC_DIVISION: 'ACC_DIVISION',
  AMC_AddOn: 'AMC_AddOn',
  AMC_AddOnComp: 'AMC_AddOnComp',
  AMC_NewLoct: 'AMC_NewLoct',
  BluestarAccounts_Sum: 'BluestarAccounts_Sum',
  CATEGORY_SRV: 'CATEGORY_SRV',
  COMPETITOR: 'COMPETITOR',
  COMPLAINT: 'COMPLAINT',
  CRM: 'CRM',
  Cust_Fresh: 'Cust_Fresh',
  DEBIT_DIV: 'DEBIT_DIV',
  DEDUCTION_HEAD: 'DEDUCTION_HEAD',
  ENQUIRY: 'ENQUIRY',
  EnqComp: 'EnqComp',
  ExecTarget: 'ExecTarget',
  FAILURE: 'FAILURE',
  Funnel_div: 'Funnel_div',
  Funnel_sum: 'Funnel_sum',
  Gateway_div: 'Gateway_div',
  Gateway_sum: 'Gateway_sum',
  HistorySection: 'HistorySection',
  History_AddOnDet: 'History_AddOnDet',
  History_AmcDet: 'History_AmcDet',
  History_Sum: 'History_Sum',
  InwInst_Param: 'InwInst_Param',
  Inwarranty_NewLoct: 'Inwarranty_NewLoct',
  Inwarranty_SerDates: 'Inwarranty_SerDates',
  Inwarranty_TrDam: 'Inwarranty_TrDam',
  Inwarranty_div: 'Inwarranty_div',
  MovementRegister_Products: 'MovementRegister_Products',
  MovementRegister_Sum: 'MovementRegister_Sum',
  NATURE_SERVICE: 'NATURE_SERVICE',
  NAT_COMPLAINT: 'NAT_COMPLAINT',
  ORG_DIV: 'ORG_DIV',
  OrderForm: 'OrderForm',
  OrderForm_Mdl: 'OrderForm_Mdl',
  Order_div: 'Order_div',
  Order_pay: 'Order_pay',
  Order_sum: 'Order_sum',
  OutWnty_Comp: 'OutWnty_Comp',
  OutWnty_Machine: 'OutWnty_Machine',
  OutWnty_div: 'OutWnty_div',
  OutWnty_sum: 'OutWnty_sum',
  PRIORITY_LOI: 'PRIORITY_LOI',
  PROFORMA_MODEL: 'PROFORMA_MODEL',
  Perf_Inv_Sum: 'Perf_Inv_Sum',
  Perf_Machine: 'Perf_Machine',
  Pipeline_div: 'Pipeline_div',
  Pipeline_sum: 'Pipeline_sum',
  Proforma_Sum: 'Proforma_Sum',
  Rel_Statistics: 'Rel_Statistics',
  Rpt_AmcTmp: 'Rpt_AmcTmp',
  SEGMENT: 'SEGMENT',
  SER_VISIT: 'SER_VISIT',
  SOMS: 'SOMS',
  SPARE: 'SPARE',
  ServiceRpt_Complaint: 'ServiceRpt_Complaint',
  ServiceRpt_DebitDiv: 'ServiceRpt_DebitDiv',
  ServiceRpt_Failure: 'ServiceRpt_Failure',
  ServiceRpt_Measure: 'ServiceRpt_Measure',
  ServiceRpt_OrgDiv: 'ServiceRpt_OrgDiv',
  ServiceRpt_Spare: 'ServiceRpt_Spare',
  ServiceRpt_Visit: 'ServiceRpt_Visit',
  ServiceRpt_sum: 'ServiceRpt_sum',
  SpareCost: 'SpareCost',
  TAX: 'TAX',
  TERRITORY: 'TERRITORY',
  TRANSIT_DAMAGE: 'TRANSIT_DAMAGE',
  Transaction: 'Transaction',
  Transaction_Bias: 'Transaction_Bias',
  Transaction_Machine: 'Transaction_Machine',
  Transaction_USP: 'Transaction_USP',
  USERGROUP: 'USERGROUP',
  USP: 'USP',
  UserPwd: 'UserPwd',
  VISIT: 'VISIT',
  out_warranties: 'out_warranties',
  out_warranty_machines: 'out_warranty_machines',
  out_warranty_components: 'out_warranty_components',
  out_warranty_payments: 'out_warranty_payments',
  service_reports: 'service_reports',
  service_details: 'service_details',
  service_schedules: 'service_schedules',
  history_cards: 'history_cards',
  history_sections: 'history_sections',
  sales_leads: 'sales_leads',
  sales_opportunities: 'sales_opportunities',
  sales_prospects: 'sales_prospects',
  sales_orders: 'sales_orders',
  users: 'users',
  PasswordResetToken: 'PasswordResetToken',
  ServiceVisitType: 'ServiceVisitType',
  ComplaintType: 'ComplaintType',
  ComplaintNatureType: 'ComplaintNatureType',
  FailureType: 'FailureType',
  Territory: 'Territory',
  Segment: 'Segment',
  Competitor: 'Competitor',
  PriorityType: 'PriorityType',
  EnquiryType: 'EnquiryType',
  DeductionType: 'DeductionType',
  DebitDivision: 'DebitDivision',
  AccountDivision: 'AccountDivision',
  SparePart: 'SparePart',
  TaxRate: 'TaxRate',
  TransitDamageType: 'TransitDamageType',
  UserGroup: 'UserGroup',
  UspType: 'UspType',
  VisitType: 'VisitType',
  ServiceCenter: 'ServiceCenter',
  HistoryRepair: 'HistoryRepair',
  HistoryMaintenance: 'HistoryMaintenance',
  HistoryWaterWash: 'HistoryWaterWash',
  HistoryAmcDetail: 'HistoryAmcDetail',
  HistoryAddonDetail: 'HistoryAddonDetail',
  HistoryAudit: 'HistoryAudit',
  HistoryComplaint: 'HistoryComplaint',
  HistoryComponentReplacement: 'HistoryComponentReplacement',
  EmailTemplate: 'EmailTemplate',
  NotificationPreference: 'NotificationPreference',
  SalesNotificationEvent: 'SalesNotificationEvent',
  SalesNotificationQueue: 'SalesNotificationQueue',
  quotations: 'quotations',
  quotation_items: 'quotation_items',
  EmailLog: 'EmailLog',
  activity_logs: 'activity_logs',
  Account: 'Account',
  Session: 'Session',
  VerificationToken: 'VerificationToken',
  SpareType: 'SpareType',
  MeasurementType: 'MeasurementType',
  scheduled_reports: 'scheduled_reports',
  scheduled_report_executions: 'scheduled_report_executions',
  report_formulas: 'report_formulas',
  report_formula_fields: 'report_formula_fields',
  formula_test_cases: 'formula_test_cases'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }

        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
