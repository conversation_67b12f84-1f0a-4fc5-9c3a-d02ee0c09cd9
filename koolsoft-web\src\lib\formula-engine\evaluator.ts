/**
 * Formula Evaluator for KoolSoft Report Formula Engine
 * 
 * This module provides safe evaluation of parsed formula ASTs with
 * built-in functions and data context support.
 */

import { FormulaAST } from './parser';
import { FormulaFunctions } from './functions';

export interface FormulaContext {
  [key: string]: any;
}

export class FormulaEvaluationError extends Error {
  constructor(message: string, public node?: FormulaAST) {
    super(message);
    this.name = 'FormulaEvaluationError';
  }
}

export class FormulaEvaluator {
  private functions: FormulaFunctions;
  private context: FormulaContext;

  constructor(context: FormulaContext = {}) {
    this.functions = new FormulaFunctions();
    this.context = context;
  }

  /**
   * Evaluate a formula AST with the given context
   */
  evaluate(ast: FormulaAST, context?: FormulaContext): any {
    if (context) {
      this.context = { ...this.context, ...context };
    }

    return this.evaluateNode(ast);
  }

  /**
   * Update the evaluation context
   */
  setContext(context: FormulaContext): void {
    this.context = context;
  }

  /**
   * Add variables to the evaluation context
   */
  addToContext(variables: FormulaContext): void {
    this.context = { ...this.context, ...variables };
  }

  /**
   * Evaluate a single AST node
   */
  private evaluateNode(node: FormulaAST): any {
    switch (node.type) {
      case 'Literal':
        return node.value;

      case 'Identifier':
        return this.evaluateIdentifier(node);

      case 'BinaryExpression':
        return this.evaluateBinaryExpression(node);

      case 'UnaryExpression':
        return this.evaluateUnaryExpression(node);

      case 'FunctionCall':
        return this.evaluateFunctionCall(node);

      default:
        throw new FormulaEvaluationError(`Unknown node type: ${(node as any).type}`, node);
    }
  }

  /**
   * Evaluate identifier (variable lookup)
   */
  private evaluateIdentifier(node: FormulaAST): any {
    if (!node.name) {
      throw new FormulaEvaluationError('Identifier node missing name', node);
    }

    // Support nested property access (e.g., customer.name)
    const parts = node.name.split('.');
    let value = this.context;

    for (const part of parts) {
      if (value === null || value === undefined) {
        throw new FormulaEvaluationError(`Cannot access property '${part}' of ${value}`, node);
      }

      if (typeof value !== 'object' || !(part in value)) {
        throw new FormulaEvaluationError(`Undefined variable: ${node.name}`, node);
      }

      value = value[part];
    }

    return value;
  }

  /**
   * Evaluate binary expression
   */
  private evaluateBinaryExpression(node: FormulaAST): any {
    if (!node.left || !node.right || !node.operator) {
      throw new FormulaEvaluationError('Binary expression missing operands or operator', node);
    }

    const left = this.evaluateNode(node.left);
    const right = this.evaluateNode(node.right);

    switch (node.operator) {
      case '+':
        return this.add(left, right);
      case '-':
        return this.subtract(left, right);
      case '*':
        return this.multiply(left, right);
      case '/':
        return this.divide(left, right);
      case '%':
        return this.modulo(left, right);
      case '^':
        return this.power(left, right);
      default:
        throw new FormulaEvaluationError(`Unknown binary operator: ${node.operator}`, node);
    }
  }

  /**
   * Evaluate unary expression
   */
  private evaluateUnaryExpression(node: FormulaAST): any {
    if (!node.argument || !node.operator) {
      throw new FormulaEvaluationError('Unary expression missing argument or operator', node);
    }

    const argument = this.evaluateNode(node.argument);

    switch (node.operator) {
      case '+':
        return +argument;
      case '-':
        return -argument;
      default:
        throw new FormulaEvaluationError(`Unknown unary operator: ${node.operator}`, node);
    }
  }

  /**
   * Evaluate function call
   */
  private evaluateFunctionCall(node: FormulaAST): any {
    if (!node.name || !node.arguments) {
      throw new FormulaEvaluationError('Function call missing name or arguments', node);
    }

    const args = node.arguments.map(arg => this.evaluateNode(arg));
    
    if (!this.functions.hasFunction(node.name)) {
      throw new FormulaEvaluationError(`Unknown function: ${node.name}`, node);
    }

    try {
      return this.functions.callFunction(node.name, args);
    } catch (error) {
      throw new FormulaEvaluationError(
        `Error calling function ${node.name}: ${error.message}`,
        node
      );
    }
  }

  /**
   * Safe addition with type coercion
   */
  private add(left: any, right: any): any {
    // String concatenation
    if (typeof left === 'string' || typeof right === 'string') {
      return String(left) + String(right);
    }

    // Numeric addition
    const leftNum = this.toNumber(left);
    const rightNum = this.toNumber(right);
    return leftNum + rightNum;
  }

  /**
   * Safe subtraction
   */
  private subtract(left: any, right: any): number {
    const leftNum = this.toNumber(left);
    const rightNum = this.toNumber(right);
    return leftNum - rightNum;
  }

  /**
   * Safe multiplication
   */
  private multiply(left: any, right: any): number {
    const leftNum = this.toNumber(left);
    const rightNum = this.toNumber(right);
    return leftNum * rightNum;
  }

  /**
   * Safe division
   */
  private divide(left: any, right: any): number {
    const leftNum = this.toNumber(left);
    const rightNum = this.toNumber(right);
    
    if (rightNum === 0) {
      throw new FormulaEvaluationError('Division by zero');
    }
    
    return leftNum / rightNum;
  }

  /**
   * Safe modulo
   */
  private modulo(left: any, right: any): number {
    const leftNum = this.toNumber(left);
    const rightNum = this.toNumber(right);
    
    if (rightNum === 0) {
      throw new FormulaEvaluationError('Modulo by zero');
    }
    
    return leftNum % rightNum;
  }

  /**
   * Safe exponentiation
   */
  private power(left: any, right: any): number {
    const leftNum = this.toNumber(left);
    const rightNum = this.toNumber(right);
    return Math.pow(leftNum, rightNum);
  }

  /**
   * Convert value to number with error handling
   */
  private toNumber(value: any): number {
    if (typeof value === 'number') {
      return value;
    }

    if (typeof value === 'string') {
      const num = parseFloat(value);
      if (isNaN(num)) {
        throw new FormulaEvaluationError(`Cannot convert '${value}' to number`);
      }
      return num;
    }

    if (typeof value === 'boolean') {
      return value ? 1 : 0;
    }

    if (value === null || value === undefined) {
      return 0;
    }

    throw new FormulaEvaluationError(`Cannot convert ${typeof value} to number`);
  }

  /**
   * Get available function names
   */
  getAvailableFunctions(): string[] {
    return this.functions.getFunctionNames();
  }

  /**
   * Get function documentation
   */
  getFunctionDoc(name: string): string | undefined {
    return this.functions.getFunctionDoc(name);
  }

  /**
   * Validate that all identifiers in the AST exist in context
   */
  validateContext(ast: FormulaAST): string[] {
    const missingVars: string[] = [];
    this.collectIdentifiers(ast, missingVars);
    return missingVars;
  }

  /**
   * Collect all identifiers from AST and check if they exist in context
   */
  private collectIdentifiers(node: FormulaAST, missing: string[]): void {
    switch (node.type) {
      case 'Identifier':
        if (node.name && !this.hasInContext(node.name)) {
          missing.push(node.name);
        }
        break;

      case 'BinaryExpression':
        if (node.left) this.collectIdentifiers(node.left, missing);
        if (node.right) this.collectIdentifiers(node.right, missing);
        break;

      case 'UnaryExpression':
        if (node.argument) this.collectIdentifiers(node.argument, missing);
        break;

      case 'FunctionCall':
        if (node.arguments) {
          node.arguments.forEach(arg => this.collectIdentifiers(arg, missing));
        }
        break;
    }
  }

  /**
   * Check if identifier exists in context (supports nested properties)
   */
  private hasInContext(identifier: string): boolean {
    const parts = identifier.split('.');
    let value = this.context;

    for (const part of parts) {
      if (value === null || value === undefined || typeof value !== 'object' || !(part in value)) {
        return false;
      }
      value = value[part];
    }

    return true;
  }
}
